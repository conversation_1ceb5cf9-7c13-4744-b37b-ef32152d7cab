﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E340F460-3005-4ABD-B511-A8AA4D749F68}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>iTong.AppUnion</RootNamespace>
    <AssemblyName>AppUnion</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NET452;AB;FG;TEST1;CEF;NET46;NET40;NET47;DPI;SCNEW;</DefineConstants>
    <DebugType>full</DebugType>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NET452;AB;FG;TEST1;CEF;NET46;NET40;NET47;DPI1;SCNEW;</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NET452;AB;FG;TEST1;CEF;NET46;NET40;NET47;DPI1;SCNEW;</DefineConstants>
    <DebugType>full</DebugType>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE;NET452;AB;FG;TEST1;CEF;NET46;NET40;NET47;DPI1;SCNEW;</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CoreWebView2, Version=0.1.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\Temp\CoreWebView2.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Charsets, Version=7.2.4630.5, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Dns\IKVM.OpenJDK.Charsets.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Core, Version=7.2.4630.5, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Dns\IKVM.OpenJDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Text, Version=7.2.4630.5, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Dns\IKVM.OpenJDK.Text.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.OpenJDK.Util, Version=7.2.4630.5, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Dns\IKVM.OpenJDK.Util.dll</HintPath>
    </Reference>
    <Reference Include="IKVM.Runtime, Version=7.2.4630.5, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Dns\IKVM.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="jmdns, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Dns\jmdns.dll</HintPath>
    </Reference>
    <Reference Include="QRCoder, Version=1.3.7.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Rtc\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.103.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\CoreMisc\IncludeDlls\System.Data.SQLite.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\Mcp\McpHub\McpClass.cs">
      <Link>zServer\API\Info\McpClass.cs</Link>
    </Compile>
    <Compile Include="ImageEdit\ImageEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImageEdit\ImageEditForm.designer.cs">
      <DependentUpon>ImageEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ImageEdit\ImageManage.cs" />
    <Compile Include="ImageEdit\tbImageEditPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="zBackup\BackupMgrMonitor.cs" />
    <Compile Include="zBackup\DB\BaseDB.cs" />
    <Compile Include="zBackup\DB\dbAddressBook.cs" />
    <Compile Include="zBackup\DB\dbCallHistory.cs" />
    <Compile Include="zBackup\DB\dbNotes.cs" />
    <Compile Include="zBackup\DB\dbSMS.cs" />
    <Compile Include="zBackup\BackupMgr.cs" />
    <Compile Include="zClass\MyAirPlayUsbMonitor.cs" />
    <Compile Include="zClass\MyAirPlayUsbSetupAPI.cs" />
    <Compile Include="zClass\MyAirPlayUsbVendor.cs" />
    <Compile Include="zClass\MyUSB.cs" />
    <Compile Include="zClass\MyUSBEx.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="zClass\MyIcon.cs" />
    <Compile Include="zClass\MyRegistry.cs" />
    <Compile Include="zClass\SettingMgr\KeyName_BIZ.cs" />
    <Compile Include="zControl\SimpleTextBoxWithScroll.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zForm\skSplashBoxNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zServer\API\FGKidAPI.cs" />
    <Compile Include="zServer\API\GiAPI.cs" />
    <Compile Include="zServer\API\Url\MyUrlExtention.cs" />
    <Compile Include="zServer\API\Url\MyUrlForFGKid.cs" />
    <Compile Include="zServer\API\_UserBaseEx.cs" />
    <Compile Include="zServer\Mcp\McpTool.cs" />
    <Compile Include="zServer\API\Info\MyTest.cs" />
    <Compile Include="zServer\RS\RsControllerGI.cs" />
    <Compile Include="zService\Service_GI.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zSystem\AmsMgr.cs" />
    <Compile Include="zSystem\DirectoryInfoMgr.cs" />
    <Compile Include="zSystem\PatchClass.cs" />
    <Compile Include="zSystem\Internal\TaskScheduler.cs" />
    <Compile Include="zFormRS\HookMgr.cs" />
    <Compile Include="zServer\API\Info\InfoForBiz.cs" />
    <Compile Include="zServer\API\Info\InfoForUser.cs" />
    <Compile Include="zServer\API\Url\MyUrlForPersonal.cs" />
    <Compile Include="zSystem\AppHelper.cs" />
    <Compile Include="zSystem\AppMgr.cs" />
    <Compile Include="zServer\RS\RsControllerBiz.cs" />
    <Compile Include="zServer\TD\tdActionHelper.cs" />
    <Compile Include="zServer\TD\tdActionModeKey.cs" />
    <Compile Include="zServer\TD\tdActionItem.cs" />
    <Compile Include="zService\ParaArgs.cs" />
    <Compile Include="zSystem\PatchMgr.cs" />
    <Compile Include="zSystem\TaskSchedulerMgr.cs" />
    <Compile Include="zSystem\TaskSchedulerMgr_System.cs" />
    <Compile Include="zSystem\UwpMgr.cs" />
    <Compile Include="zSystem\RegEditMgr.cs" />
    <Compile Include="zSystem\Internal\Wlan\Wlan.cs" />
    <Compile Include="zSystem\Internal\Wlan\WlanSetting.cs" />
    <Compile Include="zSystem\Internal\WUAPI.cs" />
    <Compile Include="zSystem\WlanMgr.cs" />
    <Compile Include="zWebRTC\MyProcesss.cs" />
    <Compile Include="zWebRTC\TimerMgr.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zWinSDK\AppCapability.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <Compile Include="QianQianLrc\EncodingHelper.cs" />
    <Compile Include="QianQianLrc\QianQianLrcer.cs" />
    <Compile Include="RingMaker\RingMakerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RingMaker\RingMakerForm.designer.cs">
      <DependentUpon>RingMakerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="RingMaker\RingMakerFormAndroid.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RingMaker\RingMakerFormAndroid.designer.cs">
      <DependentUpon>RingMakerFormAndroid.cs</DependentUpon>
    </Compile>
    <Compile Include="RingMaker\tbRingPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RingMaker\tbRingPanel.designer.cs">
      <DependentUpon>tbRingPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="zClass\ActionEnum.cs" />
    <Compile Include="zClass\ActionHelper.cs" />
    <Compile Include="zClass\ActionItem.cs" />
    <Compile Include="zClass\AirPlayDevice.cs" />
    <Compile Include="zClass\BluetoothDevice.cs" />
    <Compile Include="zClass\BluetoothDevice_Array.cs" />
    <Compile Include="zClass\BluetoothDevice_iPad_1400.cs" />
    <Compile Include="zClass\BluetoothDevice_iPhone_1400.cs" />
    <Compile Include="zClass\ChargeHelperForCast.cs" />
    <Compile Include="zClass\DllImport.cs" />
    <Compile Include="zClass\DllImportSystem.cs" />
    <Compile Include="zClass\INetFwMgr.cs" />
    <Compile Include="zClass\IniHelper.cs" />
    <Compile Include="zClass\MyAirplay.cs" />
    <Compile Include="zClass\MyAirplayUSB.cs" />
    <Compile Include="zClass\MyAirPlayUSBAndroid.cs" />
    <Compile Include="zClass\MyAirPlayUsbAndroidEnum.cs" />
    <Compile Include="zClass\MyAirPlayWiFi.cs" />
    <Compile Include="zClass\MyAudio.cs" />
    <Compile Include="zClass\MyAuthorize.cs" />
    <Compile Include="zClass\MyBluetooth.cs" />
    <Compile Include="zClass\MyClass.cs" />
    <Compile Include="zClass\MyCode.cs" />
    <Compile Include="zClass\SettingMgr\KeyName_RS.cs" />
    <Compile Include="zClass\SettingMgr\SettingMgr.cs" />
    <Compile Include="zCommon\_MDM.cs" />
    <Compile Include="zControl\skTextBoxNew.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zFormRS\frmConnect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormRS\frmConnect.Designer.cs">
      <DependentUpon>frmConnect.cs</DependentUpon>
    </Compile>
    <Compile Include="zFormRS\frmSafeMode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormRS\frmSafeMode.Designer.cs">
      <DependentUpon>frmSafeMode.cs</DependentUpon>
    </Compile>
    <Compile Include="zFormRS\frmVoiceMsg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormRS\frmVoiceMsg.Designer.cs">
      <DependentUpon>frmVoiceMsg.cs</DependentUpon>
    </Compile>
    <Compile Include="zFormRS\RsCommon.cs" />
    <Compile Include="zForm\MyDpi.cs" />
    <Compile Include="zClass\MyForm.cs" />
    <Compile Include="zClass\MyMapping.cs" />
    <Compile Include="zClass\MyScript.cs" />
    <Compile Include="zClass\MySystem.cs" />
    <Compile Include="zClass\MyTask.cs" />
    <Compile Include="zClass\MyUpdate.cs" />
    <Compile Include="zServer\API\BizAPI.cs" />
    <Compile Include="zServer\API\Url\MyUrlForBiz.cs" />
    <Compile Include="zServer\RS\RsClientStatic.cs" />
    <Compile Include="zServer\API\Url\MyUrlForKid.cs" />
    <Compile Include="zServer\API\Url\MyUrlForRs.cs" />
    <Compile Include="zServer\API\Url\MyUrl.cs" />
    <Compile Include="zControl\GuiConverter.cs" />
    <Compile Include="zControl\GuiDllImport.cs" />
    <Compile Include="zControl\GuiEnums.cs" />
    <Compile Include="zControl\GuiHelper.cs" />
    <Compile Include="zControl\GuiStructure.cs" />
    <Compile Include="zControl\MyAPI.cs" />
    <Compile Include="zControl\MyColor.cs" />
    <Compile Include="zControl\MyControl.cs" />
    <Compile Include="zControl\MyFont.cs" />
    <Compile Include="zControl\MyResource.cs" />
    <Compile Include="zControl\skBatteryBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skButtonType.cs" />
    <Compile Include="zControl\skComboBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skContextMenuStrip.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skControlBorder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skControlKey.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skDataGridViewWithScroll.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zControl\skDataGridViewWithScroll.Designer.cs">
      <DependentUpon>skDataGridViewWithScroll.cs</DependentUpon>
    </Compile>
    <Compile Include="zControl\skFlowLayoutPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skFlowLayoutPanelWithHScroll.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zControl\skFlowLayoutPanelWithScroll.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zControl\skFlowLayoutPanelWithScroll.Designer.cs">
      <DependentUpon>skFlowLayoutPanelWithScroll.cs</DependentUpon>
    </Compile>
    <Compile Include="zControl\skLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skLinkLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skListView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skPanelGuid.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skPictureBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skProgressBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skRichTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skScrollbar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\skVerticalScrollbar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\TableView\skCell.cs" />
    <Compile Include="zControl\TableView\skCellBatteryBar.cs" />
    <Compile Include="zControl\TableView\skCellBorder.cs" />
    <Compile Include="zControl\TableView\skCellButton.cs" />
    <Compile Include="zControl\TableView\skCellPictureBox.cs" />
    <Compile Include="zControl\TableView\skCellProgressBar.cs" />
    <Compile Include="zControl\TableView\skTableCell.cs" />
    <Compile Include="zControl\TableView\skTableCellButton.cs" />
    <Compile Include="zControl\TableView\skTableCellCheckBox.cs" />
    <Compile Include="zControl\TableView\skTableCellHeader.cs" />
    <Compile Include="zControl\TableView\skTableCellImage.cs" />
    <Compile Include="zControl\TableView\skTableCellProgress.cs" />
    <Compile Include="zControl\TableView\skTableCellTextBox.cs" />
    <Compile Include="zControl\TableView\skTableColumn.cs" />
    <Compile Include="zControl\TableView\skTableRow.cs" />
    <Compile Include="zControl\TableView\skTableRowComparer.cs" />
    <Compile Include="zControl\TableView\skTableView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\TableView\skTableViewArgs.cs" />
    <Compile Include="zControl\TableView\TreeView\pdTreeNode.cs" />
    <Compile Include="zControl\TableView\TreeView\pdTreeNode1.cs" />
    <Compile Include="zControl\TableView\TreeView\pdTreeNode2.cs" />
    <Compile Include="zControl\TableView\TreeView\pdTreeNode3.cs" />
    <Compile Include="zControl\TableView\TreeView\pdTreeNodeCollection.cs" />
    <Compile Include="zControl\TableView\TreeView\pdTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\TableView\TreeView\pdTreeViewArgs.cs" />
    <Compile Include="zControl\TreeView\skTreeNode.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\TreeView\skTreeNodeCollection.cs" />
    <Compile Include="zControl\TreeView\skTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zControl\TreeView\skTreeViewArgs.cs" />
    <Compile Include="zFile\FileUploadEnum.cs" />
    <Compile Include="zFile\FileUploadTask.cs" />
    <Compile Include="zFile\FileUploadDB.cs" />
    <Compile Include="zFile\FileUploadMgr.cs" />
    <Compile Include="zFile\FileAPI_QiNiu.cs" />
    <Compile Include="zFile\FileUploadTaskEx.cs" />
    <Compile Include="zFormFunc\frmBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormFunc\frmDevice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormFunc\frmMessage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormFunc\frmMessage.Designer.cs">
      <DependentUpon>frmMessage.cs</DependentUpon>
    </Compile>
    <Compile Include="zFormFunc\MsgBoxMgr.cs" />
    <Compile Include="zFormFunc\NotifyMgr.cs" />
    <Compile Include="zForm\skBaseGuiForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zForm\skBaseGuiForm.Designer.cs">
      <DependentUpon>skBaseGuiForm.cs</DependentUpon>
    </Compile>
    <Compile Include="zForm\skContainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zForm\skContainForm.Designer.cs">
      <DependentUpon>skContainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="zForm\skForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zForm\skFormBase.cs" />
    <Compile Include="zForm\skInputBox.cs" />
    <Compile Include="zForm\skMessageBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zForm\skMsgInfo.cs" />
    <Compile Include="zFormFunc\frmNotify.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFormFunc\frmNotify.designer.cs">
      <DependentUpon>frmNotify.cs</DependentUpon>
    </Compile>
    <Compile Include="zForm\skSplashBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zForm\skTransparentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zForm\skTransparentForm.Designer.cs">
      <DependentUpon>skTransparentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="zPC\ActionClass.cs" />
    <Compile Include="zPC\InputClass.cs" />
    <Compile Include="zPC\InputCode.cs" />
    <Compile Include="zPC\InputEnum.cs" />
    <Compile Include="zPC\InputScanCode.cs" />
    <Compile Include="zPC\InputSender.cs" />
    <Compile Include="zPC\InputStruct.cs" />
    <Compile Include="zFile\FileAPI.cs" />
    <Compile Include="zFile\FileAPI_S3.cs" />
    <Compile Include="zServer\API\KidAPI.cs" />
    <Compile Include="zServer\RS\RsClient.cs" />
    <Compile Include="zServer\RS\RsClientInput.cs" />
    <Compile Include="zServer\RS\RsController.cs" />
    <Compile Include="zServer\RS\RsControllerRTC.cs" />
    <Compile Include="zServer\API\Info\InfoForKid.cs" />
    <Compile Include="zServer\TaskMgr.cs" />
    <Compile Include="zService\ParaMgr.cs" />
    <Compile Include="zService\Service_MDM.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zService\ServiceMgr.cs" />
    <Compile Include="zService\Service_Watcher.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zService\Service_RS.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zService\ServiceUtil.cs" />
    <Compile Include="zSocketLocal\SocketArgs.cs" />
    <Compile Include="zSocketLocal\SocketHandlerRecv.cs" />
    <Compile Include="zSocketLocal\SocketMgr.cs" />
    <Compile Include="zServer\API\_UserBase.cs" />
    <Compile Include="zSocketLocal\SocketHandler.cs" />
    <Compile Include="zSocketLocal\SocketMgrForClient.cs" />
    <Compile Include="zSocketLocal\SocketMgrForService.cs" />
    <Compile Include="zSocket\Class\MsgKey.cs" />
    <Compile Include="zSocket\Class\MsgType.cs" />
    <Compile Include="zSocket\Class\PushEvent.cs" />
    <Compile Include="zCommon\_ClassExtend.cs" />
    <Compile Include="zSocket\Enum\skWallpaperResult.cs" />
    <Compile Include="zSocket\Enum\skWallpaperStatus.cs" />
    <Compile Include="zSocket\frmVNC.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSocket\frmVNC.designer.cs">
      <DependentUpon>frmVNC.cs</DependentUpon>
    </Compile>
    <Compile Include="zSocket\frmVNCControl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zServer\API\Info\InfoForRS.cs" />
    <Compile Include="zFile\skFileUploadInfo.cs" />
    <Compile Include="zSocket\Info\skDevice_Info.cs" />
    <Compile Include="zSocket\RS\PushKey.cs" />
    <Compile Include="zSocket\RS\RSEvent.cs" />
    <Compile Include="zSocket\RS\RSKey.cs" />
    <Compile Include="zSocket\Class\skGestureArgs.cs" />
    <Compile Include="zSocket\Class\skGestureRtcArgs.cs" />
    <Compile Include="zSocket\Class\skSocketBodyForPush.cs" />
    <Compile Include="zSocket\Class\skSocketBodyForRS.cs" />
    <Compile Include="zSocket\Class\skSocketDataForPush.cs" />
    <Compile Include="zSocket\Class\skSocketDataForRS.cs" />
    <Compile Include="zSocket\Class\skSocketMsgBase.cs" />
    <Compile Include="zSocket\Class\skSocketMsgForPush.cs" />
    <Compile Include="zSocket\Class\skSocketMsgForRS.cs" />
    <Compile Include="zSocket\Class\skTcpListener.cs" />
    <Compile Include="zSocket\Class\skWebSocket.cs" />
    <Compile Include="zSocket\RS\skWebSocketForPush.cs" />
    <Compile Include="zSocket\RS\skWebSocketForPushRSHost.cs" />
    <Compile Include="zSocket\RS\skWebSocketForRS.cs" />
    <Compile Include="zSocket\RS\skWebSocketForRSHost.cs" />
    <Compile Include="zSocket\RS\skWebSocketForRS_Send.cs" />
    <Compile Include="zWave\FFMpegHelper.cs" />
    <Compile Include="zWave\WaveFileWriter.cs" />
    <Compile Include="zWave\WaveInterop.cs" />
    <Compile Include="zCommon\AES.cs" />
    <Compile Include="zCommon\RSA.cs" />
    <Compile Include="zServer\API\RsAPI.cs" />
    <Compile Include="zSocket\RS\RSEventType.cs" />
    <Compile Include="zSocket\RS\RSStep.cs" />
    <Compile Include="zSocket\Enum\skAddonActionType.cs" />
    <Compile Include="zSocket\Enum\skAddonFeatureResult.cs" />
    <Compile Include="zSocket\Enum\skAddonStatus.cs" />
    <Compile Include="zSocket\Enum\skBroadcastStatus.cs" />
    <Compile Include="zSocket\Enum\skConnectCode.cs" />
    <Compile Include="zSocket\Enum\skDeviceType.cs" />
    <Compile Include="zSocket\Enum\skEncryptType.cs" />
    <Compile Include="zSocket\Enum\skFeatureType.cs" />
    <Compile Include="zSocket\Enum\skGestureType.cs" />
    <Compile Include="zSocket\Enum\skImeInputType.cs" />
    <Compile Include="zSocket\Enum\skImeStatus.cs" />
    <Compile Include="zSocket\Enum\skMicrophoneStatus.cs" />
    <Compile Include="zSocket\Enum\skMirrrorMode.cs" />
    <Compile Include="zSocket\Enum\skModeType.cs" />
    <Compile Include="zSocket\Enum\skMsgType.cs" />
    <Compile Include="zSocket\Enum\skRtcStatus.cs" />
    <Compile Include="zSocket\Enum\skSafeModeStatus.cs" />
    <Compile Include="zSocket\Enum\skTipType.cs" />
    <Compile Include="zSocket\Enum\skVoIPStatus.cs" />
    <Compile Include="zSocket\Info\AppBaseInfo.cs" />
    <Compile Include="zSocket\Info\AppDetailInfo.cs" />
    <Compile Include="zSocket\Info\AppUserInfo.cs" />
    <Compile Include="zSocket\Info\skDevice.cs" />
    <Compile Include="zSocket\Info\skForwardUrl.cs" />
    <Compile Include="zSocket\Info\skHeartBeatInfo.cs" />
    <Compile Include="zSocket\Info\skLbsInfo.cs" />
    <Compile Include="zSocket\Info\skLoginInfo.cs" />
    <Compile Include="zSocket\Info\skPingInfo.cs" />
    <Compile Include="zSocket\Info\skSignalInfo.cs" />
    <Compile Include="zSocket\MyApp.cs" />
    <Compile Include="zSocket\MyDevice.cs" />
    <Compile Include="zSocket\MyRS.cs" />
    <Compile Include="zSocket\MySetting.cs" />
    <Compile Include="zSocket\MySocket.cs" />
    <Compile Include="zServer\API\CastAPI.cs" />
    <Compile Include="zServer\ServerPush.cs" />
    <Compile Include="zWave\WaveMgr.cs" />
    <Compile Include="zWebPage\AudioPlayerMgr.cs" />
    <Compile Include="zWebPage\frmChat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWebPage\frmChat.Designer.cs">
      <DependentUpon>frmChat.cs</DependentUpon>
    </Compile>
    <Compile Include="zWebPage\frmChatWeb.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWebPage\frmFeedback.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWebPage\frmFeedback.designer.cs">
      <DependentUpon>frmFeedback.cs</DependentUpon>
    </Compile>
    <Compile Include="zWebPage\frmPage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWebPage\frmPage.Designer.cs">
      <DependentUpon>frmPage.cs</DependentUpon>
    </Compile>
    <Compile Include="zWebPage\RecordDB.cs" />
    <Compile Include="zWebPage\PageEnum.cs" />
    <Compile Include="zWebPage\PageHelper.cs" />
    <Compile Include="zWebPage\RecordDetail.cs" />
    <Compile Include="zWebPage\RecordFileOperation.cs" />
    <Compile Include="zWebPage\RecordFileState.cs" />
    <Compile Include="zWebPage\RecordMgr.cs" />
    <Compile Include="zWebPage\RecordFromType.cs" />
    <Compile Include="zWebPage\RecordTask.cs" />
    <Compile Include="zWebPage\RecordTipType.cs" />
    <Compile Include="zWebPage\RecordType.cs" />
    <Compile Include="zWebRTC\LivingClient.cs" />
    <Compile Include="zWebRTC\LivingEnum.cs" />
    <Compile Include="zWebRTC\LivingEventArgs.cs" />
    <Compile Include="zWebRTC\MqttEnum.cs" />
    <Compile Include="zWebRTC\MqttEventArgs.cs" />
    <Compile Include="zWebRTC\MqttPassword.cs" />
    <Compile Include="zWebRTC\MqttRTCClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\IMqttChannelAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\IMqttClientAdapterFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\IMqttServerAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\MqttChannelAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\MqttConnectingFailedException.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\MqttPacketInspector.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\ReceivedMqttPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Certificates\BlobCertificateProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Certificates\ICertificateProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Certificates\X509CertificateProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Channel\IMqttChannel.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectingEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectResultCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectReason.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Exceptions\MqttClientDisconnectedException.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Exceptions\MqttClientUnexpectedDisconnectReceivedException.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\ExtendedAuthenticationExchange\IMqttExtendedAuthenticationExchangeHandler.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\ExtendedAuthenticationExchange\MqttExtendedAuthenticationExchangeContext.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\ExtendedAuthenticationExchange\MqttExtendedAuthenticationExchangeData.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\IMqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttClientConnectionStatus.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttClientExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttPacketIdentifierProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\IMqttClientChannelOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\IMqttClientCredentialsProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientCertificateValidationEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientCredentials.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientDefaultCertificateValidationHandler.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptionsBuilderTlsParameters.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptionsBuilderWebSocketParameters.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientTcpOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientTcpOptionsExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientTlsOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientWebSocketOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientWebSocketProxyOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Publishing\MqttClientPublishReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Publishing\MqttClientPublishResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Publishing\MqttClientPublishResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Receiving\MqttApplicationMessageReceivedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Receiving\MqttApplicationMessageReceivedReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResultCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResultItem.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResultCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResultItem.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\IMqttNetLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetEventLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetLogLevel.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetLogMessage.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetLogMessagePublishedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetNullLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetSourceLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetSourceLoggerExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\PacketInspection\InspectMqttPacketEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\PacketInspection\MqttPacketFlowDirection.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Runtime\TargetFrameworkProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttCommunicationException.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttCommunicationTimedOutException.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttConfigurationException.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttProtocolViolationException.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\IMqttPacketFormatter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttApplicationMessageFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttBufferReader.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttBufferWriter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttConnAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttConnectPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttConnectReasonCodeConverter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttDisconnectPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttFixedHeader.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPacketBuffer.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPacketFactories.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPacketFormatterAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttProtocolVersion.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubCompPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPublishPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubRecPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubRelPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttSubAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttSubscribePacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttUnsubAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttUnsubscribePacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\ReadFixedHeaderResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V3\MqttV3PacketFormatter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PacketDecoder.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PacketEncoder.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PacketFormatter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PropertiesReader.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PropertiesWriter.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\CrossPlatformSocket.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttClientAdapterFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpChannel.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpChannel.Uwp.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpServerAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpServerAdapter.Uwp.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpServerListener.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttWebSocketChannel.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncEvent.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncEventInvocator.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncLock.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncQueue.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncQueueDequeueResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncSignal.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncTaskCompletionSource.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\BlockingQueue.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\CompletedTask.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\Disposable.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\EmptyBuffer.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttPacketBus.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttPacketBusItem.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttPacketBusPartition.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttTaskTimeout.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\TaskExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\LowLevelClient\ILowLevelMqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\LowLevelClient\LowLevelMqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttApplicationMessage.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttApplicationMessageBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttApplicationMessageExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttTopicFilterBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttTopicFilterComparer.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttTopicFilterCompareResult.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\IMqttPacketAwaitable.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\MqttPacketAwaitable.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\MqttPacketAwaitableFilter.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\MqttPacketDispatcher.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttAuthPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttConnAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttConnectPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttDisconnectPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPacketWithIdentifier.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPingReqPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPingRespPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubCompPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPublishPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubRecPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubRelPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttSubAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttSubscribePacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttTopicFilter.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttUnsubAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttUnsubscribePacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttUserProperty.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttAuthenticateReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttConnectReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttConnectReturnCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttControlPacketType.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttDisconnectReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPayloadFormatIndicator.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPropertyId.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubAckReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubCompReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubRecReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubRelReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttQualityOfServiceLevel.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttRetainHandling.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttSubscribeReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttSubscribeReturnCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttTopicValidator.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttUnsubscribeReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ApplicationMessageNotConsumedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientAcknowledgedPublishPacketEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientConnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientDisconnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientSubscribedTopicEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientUnsubscribedTopicEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingPacketEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingPublishEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingSubscriptionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingUnsubscriptionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\LoadingRetainedMessagesEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\PreparingSessionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\RetainedMessageChangedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\SessionDeletedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ValidatingConnectionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\InjectedMqttApplicationMessage.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\CheckSubscriptionsResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\DispatchApplicationMessageResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\ISubscriptionChangedNotification.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClientSessionsManager.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClientStatistics.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClientSubscriptionsManager.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttRetainedMessagesManager.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttServerEventContainer.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttServerKeepAliveMonitor.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttSession.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttSubscription.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\SubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\TopicHashMaskSubscriptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\UnsubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttClientDisconnectType.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttRetainedMessageMatch.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttServer.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttServerExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\IMqttServerCertificateCredentials.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttPendingMessagesOverflowStrategy.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerCertificateCredentials.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerTcpEndpointBaseOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerTcpEndpointOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerTlsTcpEndpointOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\PublishResponse.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Status\MqttClientStatus.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Status\MqttSessionStatus.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\SubscribeResponse.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\UnsubscribeResponse.cs" />
    <Compile Include="zWebRTC\MyLiving.cs" />
    <Compile Include="zWebRTC\MyLog.cs" />
    <Compile Include="zWebRTC\MyMqtt.cs" />
    <Compile Include="zWebRTC\MyPeerTalk.cs" />
    <Compile Include="zWebRTC\MyReset.cs" />
    <Compile Include="zWebRTC\MyRTC.cs" />
    <Compile Include="zWebRTC\MyTouch.cs" />
    <Compile Include="zWebRTC\MyVirtualDesktop.cs" />
    <Compile Include="zWebRTC\RTCClient.cs" />
    <Compile Include="zWebRTC\RTCEnum.cs" />
    <Compile Include="zWebRTC\RTCErrorType.cs" />
    <Compile Include="zWebRTC\RTCEventArgs.cs" />
    <Compile Include="zWebRTC\RTCStatistics.cs" />
    <Compile Include="zWebRTC\RTCTurn.cs" />
    <EmbeddedResource Include="zControl\skFlowLayoutPanelWithHScroll.resx">
      <DependentUpon>skFlowLayoutPanelWithHScroll.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFormFunc\frmMessage.resx">
      <DependentUpon>frmMessage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFormRS\frmConnect.resx">
      <DependentUpon>frmConnect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFormRS\frmSafeMode.resx">
      <DependentUpon>frmSafeMode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFormRS\frmVoiceMsg.resx">
      <DependentUpon>frmVoiceMsg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zForm\skSplashBoxNew.resx">
      <DependentUpon>skSplashBoxNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zForm\skTransparentForm.resx">
      <DependentUpon>skTransparentForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWebPage\frmFeedback.resx">
      <DependentUpon>frmFeedback.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWebPage\frmPage.resx">
      <DependentUpon>frmPage.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ImageEdit\ImageEditForm.resx">
      <DependentUpon>ImageEditForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RingMaker\RingMakerForm.resx">
      <DependentUpon>RingMakerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RingMaker\RingMakerFormAndroid.resx">
      <DependentUpon>RingMakerFormAndroid.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RingMaker\tbRingPanel.resx">
      <DependentUpon>tbRingPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zControl\skDataGridViewWithScroll.resx">
      <DependentUpon>skDataGridViewWithScroll.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zControl\skFlowLayoutPanelWithScroll.resx">
      <DependentUpon>skFlowLayoutPanelWithScroll.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFormFunc\frmDevice.resx">
      <DependentUpon>frmDevice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zForm\skMessageBox.resx">
      <DependentUpon>skMessageBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFormFunc\frmNotify.resx">
      <DependentUpon>frmNotify.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSocket\frmVNC.resx">
      <DependentUpon>frmVNC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWebPage\frmChat.resx">
      <DependentUpon>frmChat.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ResourcesCommon\btn_close_3.png" />
    <Content Include="ResourcesCommon\btn_close_3_exit.png" />
    <Content Include="ResourcesCommon\btn_fullscreen_3.png" />
    <Content Include="ResourcesCommon\btn_min_3.png" />
    <Content Include="ResourcesCommon\chk_selected.png" />
    <Content Include="ResourcesCommon\chk_unselect.png" />
    <Content Include="ResourcesCommon\cms_bg.png" />
    <Content Include="ResourcesCommon\cms_item_2_select.png" />
    <Content Include="ResourcesCommon\ctl_btn_dot.png" />
    <Content Include="ResourcesCommon\frm_bg_shadow.png" />
    <Content Include="ResourcesCommon\icon_panda_80.png" />
    <Content Include="ResourcesCommon\pgb_dot_blue.png" />
    <Content Include="ResourcesCommon\pnl_bg_gray_line.png" />
    <Content Include="ResourcesCommon\rdo_bg_selected.png" />
    <Content Include="ResourcesCommon\rdo_selected.png" />
    <Content Include="ResourcesCommon\rdo_unselect.png" />
    <Content Include="ResourcesCommon\rdo_unselect_hover.png" />
    <Content Include="ResourcesCommon\scrollbar_h_bg.png" />
    <Content Include="ResourcesCommon\scrollbar_h_split.png" />
    <Content Include="ResourcesCommon\scrollbar_v_bg.png" />
    <Content Include="ResourcesCommon\scrollbar_v_split.png" />
    <Content Include="ResourcesCommon\tvw_battery_bg.png" />
    <Content Include="ResourcesCommon\tvw_battery_charging.png" />
    <Content Include="ResourcesCommon\tvw_drapdrop.png" />
    <Content Include="ResourcesCommon\tvw_node_close.png" />
    <Content Include="ResourcesCommon\tvw_node_open.png" />
    <Content Include="ResourceWeb\no_photo.png" />
    <None Include="ResourcesRS\logo_rs%402x.png" />
    <Content Include="ResourcesRS\btn_voice_4%402x.png" />
    <Content Include="ResourcesRS\rs_arrow_left%402x.png" />
    <Content Include="ResourcesRS\rs_arrow_right%402x.png" />
    <Content Include="ResourcesRS\rs_calling%402x.png" />
    <Content Include="ResourcesRS\rs_chk%402x.png" />
    <Content Include="ResourcesRS\rs_chk_check%402x.png" />
    <Content Include="ResourcesRS\rs_close_4%402x.png" />
    <Content Include="ResourcesRS\rs_frm_shadow%402x.png" />
    <Content Include="ResourcesRS\rs_msg_4%402x.png" />
    <Content Include="ResourcesRS\rs_msg_new_4%402x.png" />
    <Content Include="ResourcesRS\rs_reject_4%402x.png" />
    <Content Include="ResourcesRS\rs_tip%402x.png" />
    <Content Include="ResourcesRS\rs_user_head%402x.png" />
    <Content Include="ResourcesRS\rs_voip_connect%402x.png" />
    <Content Include="ResourcesRS\rs_voip_reject%402x.png" />
    <Content Include="Resources\btn_file_4%402x.png" />
    <Content Include="Resources\btn_minimize_4%402x.png" />
    <Content Include="Resources\btn_voice_4%402x.png" />
    <Content Include="Resources\ic_close%402x.png" />
    <Content Include="ResourceWeb\chat_btn_4_closure.png" />
    <Content Include="ResourceWeb\chat_btn_4_minimize.png" />
    <Content Include="ResourceWeb\chat_btn_4_phone%402x.png" />
    <None Include="ResourcesUrl\weburl_AirDroidParentalConnector" />
    <None Include="ResourcesUrl\weburl_BizDaemon" />
    <None Include="ResourcesUrl\weburl_FlashGetKidsActivator" />
    <None Include="ResourcesUrl\weburl_GoInsightDaemon" />
    <None Include="ResourcesUrl\weburl_RemoteSupport" />
    <None Include="Resources\btn_file_4.png" />
    <None Include="Resources\btn_voice_4.png" />
    <None Include="Resources\btn_phone_reject_red_4.png" />
    <None Include="Resources\GetAUMIDS.ps1" />
    <None Include="Resources\ic_calling.png" />
    <None Include="Resources\btn_clear_3.png" />
    <None Include="Resources\airdroid_48.png" />
    <None Include="Resources\airdroid.ico" />
    <Content Include="ResourcesRS\rs_arrow_down.png" />
    <Content Include="ResourcesRS\rs_arrow_left.png" />
    <Content Include="ResourcesRS\rs_arrow_right.png" />
    <Content Include="ResourcesRS\rs_arrow_up.png" />
    <Content Include="ResourcesRS\rs_btn_blue_4.png" />
    <Content Include="ResourcesRS\rs_btn_green_4.png" />
    <Content Include="ResourcesRS\rs_btn_grey_4.png" />
    <Content Include="ResourcesRS\rs_btn_red_4.png" />
    <Content Include="ResourcesRS\rs_btn_white_4.png" />
    <Content Include="ResourcesRS\rs_calling.png" />
    <Content Include="ResourcesRS\rs_calling_4.png" />
    <Content Include="ResourcesRS\rs_chk.png" />
    <Content Include="ResourcesRS\rs_chk_check.png" />
    <Content Include="ResourcesRS\rs_chk_disabled.png" />
    <Content Include="ResourcesRS\rs_chk_hover.png" />
    <Content Include="ResourcesRS\rs_close_4.png" />
    <Content Include="ResourcesRS\rs_frm_bg_shadow.png" />
    <Content Include="ResourcesRS\rs_frm_corner.png" />
    <Content Include="ResourcesRS\rs_frm_shadow.png" />
    <Content Include="ResourcesRS\rs_msg_4.png" />
    <Content Include="ResourcesRS\rs_msg_new_4.png" />
    <Content Include="ResourcesRS\rs_pic_disconnect_Icon.png" />
    <Content Include="ResourcesRS\rs_reject_4.png" />
    <Content Include="ResourcesRS\rs_tip.png" />
    <Content Include="ResourcesRS\rs_user_head.png" />
    <Content Include="ResourcesRS\rs_voip_connect.png" />
    <Content Include="ResourcesRS\rs_voip_reject.png" />
    <None Include="Resources\bg_message.png" />
    <None Include="Resources\btn_clear_4.png" />
    <Content Include="Resources\btn_closure_4.png" />
    <Content Include="Resources\btn_minimize_4.png" />
    <None Include="Resources\btn_search_3.png" />
    <Content Include="Resources\btn_next_3.png" />
    <Content Include="Resources\btn_prev_3.png" />
    <None Include="Resources\frm_bg_tip.png" />
    <None Include="Resources\ic_close.png" />
    <Content Include="Resources\loading2.gif" />
    <None Include="Resources\rs_frm_shadow.png" />
    <None Include="Resources\rs_frm_corner.png" />
    <None Include="Resources\rs_calling_4.png" />
    <None Include="Resources\rs_close_4.png" />
    <None Include="Resources\rs_tip.png" />
    <None Include="Resources\rs_arrow_down.png" />
    <None Include="Resources\rs_arrow_left.png" />
    <None Include="Resources\rs_arrow_right.png" />
    <None Include="Resources\rs_arrow_up.png" />
    <None Include="Resources\rs_calling.png" />
    <None Include="Resources\rs_btn_grey_4.png" />
    <None Include="Resources\rs_btn_white_4.png" />
    <None Include="Resources\rs_reject_4.png" />
    <None Include="Resources\pgb_wait_003.png" />
    <None Include="Resources\pgb_wait_002.png" />
    <None Include="Resources\pgb_wait_001.png" />
    <None Include="Resources\pgb_wait_000.png" />
    <None Include="Resources\pgb_wait_004.png" />
    <None Include="Resources\pgb_wait_005.png" />
    <None Include="Resources\pgb_wait_006.png" />
    <None Include="Resources\pgb_wait_007.png" />
    <None Include="Resources\pgb_wait_008.png" />
    <None Include="Resources\pgb_wait_009.png" />
    <Content Include="ResourceWeb\chat_btn_4_phone.png" />
    <None Include="ResourceWeb\chat_btn_4_send_msg.png" />
    <Content Include="ResourceWeb\chat_btn_4_voice_cancel%402x.png" />
    <Content Include="ResourceWeb\chat_btn_4_voice_ok%402x.png" />
    <Content Include="ResourceWeb\chat_gif_voice%402x.gif" />
    <Content Include="ResourceWeb\chat_gif_voice.gif" />
    <Content Include="ResourceWeb\chat_voip_connecting.png" />
    <Content Include="ResourceWeb\icon_file_3gp_32.png" />
    <Content Include="ResourceWeb\icon_file_aac_32.png" />
    <Content Include="ResourceWeb\icon_file_amr_32.png" />
    <Content Include="ResourceWeb\icon_file_apk_32.png" />
    <Content Include="ResourceWeb\icon_file_avi_32.png" />
    <Content Include="ResourceWeb\icon_file_dng_32.png" />
    <Content Include="ResourceWeb\icon_file_docx_32.png" />
    <Content Include="ResourceWeb\icon_file_doc_32.png" />
    <Content Include="ResourceWeb\icon_file_exe_32.png" />
    <Content Include="ResourceWeb\icon_file_flac_32.png" />
    <Content Include="ResourceWeb\icon_file_folder_32.png" />
    <Content Include="ResourceWeb\icon_file_gif_32.png" />
    <Content Include="ResourceWeb\icon_file_gz_32.png" />
    <Content Include="ResourceWeb\icon_file_jpeg_32.png" />
    <Content Include="ResourceWeb\icon_file_jpg_32.png" />
    <Content Include="ResourceWeb\icon_file_m4a_32.png" />
    <Content Include="ResourceWeb\icon_file_mkv_32.png" />
    <Content Include="ResourceWeb\icon_file_video_32.png" />
    <Content Include="ResourceWeb\icon_file_mov_32.png" />
    <Content Include="ResourceWeb\icon_file_mp3_32.png" />
    <Content Include="ResourceWeb\icon_file_mp4_32.png" />
    <Content Include="ResourceWeb\icon_file_music_32.png" />
    <Content Include="ResourceWeb\icon_file_opus_32.png" />
    <Content Include="ResourceWeb\icon_file_other_32.png" />
    <Content Include="ResourceWeb\icon_file_pdf_32.png" />
    <Content Include="ResourceWeb\icon_file_photo_32.png" />
    <Content Include="ResourceWeb\icon_file_pic_32.png" />
    <Content Include="ResourceWeb\icon_file_png_32.png" />
    <Content Include="ResourceWeb\icon_file_pptx_32.png" />
    <Content Include="ResourceWeb\icon_file_ppt_32.png" />
    <Content Include="ResourceWeb\icon_file_rar_32.png" />
    <Content Include="ResourceWeb\icon_file_text_32.png" />
    <Content Include="ResourceWeb\icon_file_txt_32.png" />
    <Content Include="ResourceWeb\icon_file_wav_32.png" />
    <Content Include="ResourceWeb\icon_file_wma_32.png" />
    <Content Include="ResourceWeb\icon_file_xlsx_32.png" />
    <Content Include="ResourceWeb\icon_file_xls_32.png" />
    <Content Include="ResourceWeb\icon_file_zip_32.png" />
    <None Include="ResourceWeb\audio_incoming_call" />
    <None Include="ResourceWeb\audio_message" />
    <None Include="ResourceWeb\audio_transfer_end" />
    <None Include="ResourceWeb\audio_transfer_start" />
    <None Include="ResourceWeb\chat_btn_4_voip.png" />
    <None Include="ResourceWeb\chat_btn_4_voice_ok.png" />
    <None Include="ResourceWeb\chat_btn_4_voice_cancel.png" />
    <None Include="ResourceWeb\chat_btn_4_voice.png" />
    <None Include="ResourceWeb\chat_btn_4_send_file.png" />
    <None Include="Resources\btn_blue_4.png" />
    <None Include="Resources\btn_blue_line_4.png" />
    <Content Include="Resources\btn_close.png" />
    <Content Include="Resources\btn_selectmusic_4.png" />
    <None Include="Resources\dgv_desc.png" />
    <None Include="Resources\dgv_asc.png" />
    <None Include="Resources\dgv_bg_header.png" />
    <Content Include="Resources\frm_bg_state.png" />
    <Content Include="Resources\icon_music_100.png" />
    <None Include="Resources\icon_panda_80.png" />
    <Content Include="Resources\iTong.ico" />
    <Content Include="Resources\pnl_bg_navigation.png" />
    <Content Include="Resources\ring_bg.png" />
    <Content Include="Resources\ring_coordinate.png" />
    <Content Include="Resources\ring_progress_bg.png" />
    <Content Include="Resources\ring_progress_play.png" />
    <Content Include="Resources\ring_progress_selected.png" />
    <Content Include="Resources\ring_unselect.png" />
    <Content Include="Resources\u_btn_1_blue.png" />
    <Content Include="Resources\u_btn_1_transprent.png" />
    <Content Include="Resources\u_btn_1_white.png" />
    <Content Include="Resources\u_btn_3_blue.png" />
    <Content Include="Resources\u_btn_3_close.png" />
    <Content Include="Resources\u_btn_4_blue.png" />
    <Content Include="Resources\u_btn_4_green.png" />
    <Content Include="Resources\u_btn_4_Toolbar.png" />
    <Content Include="Resources\u_btn_4_white.png" />
    <Content Include="Resources\u_form_bg_blank.png" />
    <Content Include="Resources\u_form_bg_sub.png" />
    <Content Include="Resources\u_gif_loading.gif" />
    <Content Include="Resources\u_gif_Ringtone.gif" />
    <Content Include="Resources\u_gif_searching.gif" />
    <Content Include="Resources\u_health_bg.png" />
    <Content Include="Resources\u_health_H.png" />
    <Content Include="Resources\u_health_L.png" />
    <Content Include="Resources\u_health_style1.png" />
    <Content Include="Resources\u_health_style2.png" />
    <Content Include="Resources\u_icon_1_Folder.png" />
    <Content Include="Resources\u_icon_1_sinaT.png" />
    <Content Include="Resources\u_icon_Cancel.png" />
    <Content Include="Resources\u_icon_Cut.png" />
    <Content Include="Resources\u_icon_OpenNew.png" />
    <Content Include="Resources\u_icon_TurnLeft.png" />
    <Content Include="Resources\u_icon_TurnRight.png" />
    <Content Include="Resources\u_pic_Artist_num.png" />
    <Content Include="Resources\u_pic_no_album.png" />
    <Content Include="Resources\u_pic_no_artist.png" />
    <Content Include="Resources\u_pnl_Artist.png" />
    <Content Include="Resources\u_pnl_contact.png" />
    <Content Include="Resources\u_pnl_Notify.png" />
    <Content Include="Resources\u_pnl_no_music.png" />
    <Content Include="Resources\u_pnl_Slider.png" />
    <Content Include="Resources\u_slider_blue.png" />
    <Content Include="Resources\u_slider_normal.png" />
    <Content Include="Resources\u_tvw_node_radio.png" />
    <None Include="ResourceWeb\web_page.zip" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android47.csproj">
      <Project>{b2bc5baa-e56d-412b-ab26-82e222454f3b}</Project>
      <Name>Android46</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components47.vbproj">
      <Project>{f9779807-fdba-4be9-9da2-3746bb24597b}</Project>
      <Name>Components46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc47.csproj">
      <Project>{596775d3-3eea-4125-bac2-934df77fbeba}</Project>
      <Name>CoreMisc46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS47.csproj">
      <Project>{2435928b-bf66-4791-b976-aa373477fbfe}</Project>
      <Name>CoreModuleCS46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses47.csproj">
      <Project>{a5844815-737d-486e-b7de-d57262f9e5f4}</Project>
      <Name>CoreReses46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag47.csproj">
      <Project>{7f12e495-c239-4b39-8156-aff1ad7a95b6}</Project>
      <Name>CoreTag46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS47.csproj">
      <Project>{c0b96f67-6997-454c-a762-470f156addf4}</Project>
      <Name>CoreUtilCS46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil47.vbproj">
      <Project>{0c859628-5952-44d7-8c32-ab838ef37ede}</Project>
      <Name>CoreUtil46</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone47.csproj">
      <Project>{6939a9e2-8f07-4834-9403-2cfb06ec466e}</Project>
      <Name>iPhone46</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPodDB\iPodDB47.csproj">
      <Project>{f2b1cb0a-61b7-435a-b0a0-9926865e19c5}</Project>
      <Name>iPodDB46</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf47.csproj">
      <Project>{3fd45453-6069-4dd0-bbf0-17b25606e359}</Project>
      <Name>ProtoBuf46</Name>
    </ProjectReference>
    <ProjectReference Include="..\WebSocket4NetSource\WebSocket4Net.Net47.csproj">
      <Project>{6920447f-76b1-4739-822e-9ce3a2882718}</Project>
      <Name>WebSocket4Net.Net47</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="ResourcesUrl\weburl_FlashGetCast">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="ResourcesUrl\weburl_PandaSpy">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>copy $(SolutionDir)DllImport\$(PlatformName) $(TargetDir)
copy $(SolutionDir)DllImport\$(PlatformName)\WebView2 $(TargetDir)
copy $(SolutionDir)DllImport\$(PlatformName)\Rtc $(TargetDir)

md $(TargetDir)Codec
copy $(SolutionDir)DllImport\Codec $(TargetDir)Codec

md $(TargetDir)Font
copy $(SolutionDir)DllImport\Font $(TargetDir)Font

md $(TargetDir)adb
copy $(SolutionDir)DllImport\adb $(TargetDir)adb

rd /s /q $(SolutionDir)DllImport\Temp
md $(SolutionDir)DllImport\Temp
copy $(SolutionDir)DllImport\$(PlatformName)\WebView2 $(SolutionDir)DllImport\Temp

</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>