﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;

namespace iTong.CoreModule
{
    public partial class BackupMgr
    {
        public static string TextDir { get; set; }

        public static List<string> ListSubscribeText = new List<string>();

        public static List<string> ListUserText = new List<string>();

        static BackupMgr()
        {
            TextDir = Path.Combine(Folder.CacheFolder, "Text");
        }

        public static void LoadFilterText(bool refreshFromServer = false)
        {
            if (refreshFromServer)
            {

                return;
            }
        }

        /// <summary>
        /// 根据UDID获取关键字，并从备份文件中过滤
        /// </summary>
        /// <param name="dirBackup"></param>
        /// <param name="password"></param>
        /// <param name="refreshKeywordFromServer"></param>
        public static void FilterSms(string dirBackup, string password, bool refreshKeywordFromServer = false)
        {
            try
            {
                iPhoneDevice dev = mbdb.GetDeviceFromManifestPlist(dirBackup);
                if (string.IsNullOrEmpty(dev.UniqueDeviceID))
                {
                    LogBackup("udid IsNullOrEmpty.", dirBackup);
                    return;
                }

                string dirUDID = Path.Combine(TextDir, dev.UniqueDeviceID);
                Folder.CheckFolder(dirUDID);

                string pathSubscribeText = Path.Combine(dirUDID, "SubscribeText.dat");
                string pathUserText = Path.Combine(dirUDID, "UserText.dat");



            }
            catch (Exception ex)
            {
                Common.LogException(ex, "KidAPI.FilterSms");
            }
        }
    }
}
