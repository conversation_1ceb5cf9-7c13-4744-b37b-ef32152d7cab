﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;

namespace iTong.CoreModule
{
    public class dbNotes : BaseDB
    {
        public dbNotes(string strDBFilePath) : base(strDBFilePath)
        {

        }
        public override string DBName => "notes.sqlite";

        public List<Note> GetNotes(List<string> listKeywords = null)
        {
            List<Note> listInfo = new List<Note>();
            try
            {
                if (!this.Open())
                    goto DoExit;

                string strSQL = "SELECT A.ZAUTHOR,A.ZCONTENTTYPE,A.ZCREATIONDATE,A.ZMODIFICATIONDATE,A.ZTITLE,B.ZCONTENT From ZNOTE A Left Join ZNOTEBODY B ON A.ZBODY=B.Z_PK;";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
                if (dt == null)
                {
                    Common.Log("GetNotes.ExecuteSQL -> dt = null " + strSQL, "Backup", true);
                    goto DoExit;
                }

                foreach (DataRow row in dt.Rows)
                {
                    Note info = new Note();
                    info.Author = Common.GetValue<string>(row["ZAUTHOR"], string.Empty);
                    info.Content = Common.GetValue<string>(row["ZCONTENT"], string.Empty);
                    info.ContentType = Common.GetValue<ContentType>(row["ZCONTENTTYPE"], ContentType.TextPlain);

                    info.DateCreated = this.GetDateTime(Common.GetValue<string>(row["ZCREATIONDATE"], string.Empty));
                    info.DateModified = this.GetDateTime(Common.GetValue<string>(row["ZMODIFICATIONDATE"], string.Empty));
                    info.Subject = Common.GetValue<string>(row["ZTITLE"], string.Empty);

                    if (listKeywords == null)
                    {
                        listInfo.Add(info);
                    }
                    else
                    {
                        string findText = listKeywords.Find((x) => info.Content.Contains(x) || x.Contains(info.Content) || info.Subject.Contains(x) || x.Contains(info.Subject));
                        if (!string.IsNullOrEmpty(findText))
                            listInfo.Add(info);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbNotes.LoadNotes");
            }

        DoExit:
            this.Close();

            return listInfo;
        }

        private DateTime GetDateTime(string strValue)
        {
            double dbValue = Common.GetDigit(strValue);

            return Common.ConvertToPcTime(Convert.ToInt64(dbValue)); ;
        }

    }
}
