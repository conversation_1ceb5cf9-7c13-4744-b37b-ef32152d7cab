﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;
using System.Text;


using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

#if MAC
using Image = AppKit.NSImage;
using SQLiteFunction = Mono.Data.Sqlite.SqliteFunction;
using SQLiteFunctionAttribute = Mono.Data.Sqlite.SqliteFunctionAttribute;
using FunctionType = Mono.Data.Sqlite.FunctionType;
#else
using System.Drawing;
using System.Data.SQLite;
using SQLiteConnection = System.Data.SQLite3.SQLiteConnection;
#endif

namespace iTong.CoreModule
{
    public static class BaseDBExtend
    {
        public static string FormatPhoneNumber(this string phoneNumber)
        {
            return phoneNumber.Replace(" ", "").Replace("-", "").Replace("+", "");
        }
    }

    public class BaseDB : IDisposable
    {
        #region 静态函数

        private static object mLocker = new object();
        private static Dictionary<string, BaseDB> mDictDB = new Dictionary<string, BaseDB>();

        public static T Create<T>(string strDBFilePath) where T : BaseDB
        {
            T db = null;
            //string strID = strDBFilePath.Replace("/", "").Replace(@"\", "").Replace(" ", "").Replace(".", "").Replace(":", "");
            string strID = Common.GetMd5Hex(strDBFilePath);

            try
            {
                lock (mLocker)
                {
                    if (!string.IsNullOrEmpty(strID) && mDictDB.ContainsKey(strID))
                    {
                        db = (T)mDictDB[strID];
                        if (db == null)
                        {
                            mDictDB.Remove(strID);

                            db = (T)MyClass2.CreateInstance(typeof(T), new object[] { strDBFilePath });
                            mDictDB.Add(strID, db);
                        }
                    }
                    else
                    {
                        db = (T)MyClass2.CreateInstance(typeof(T), new object[] { strDBFilePath });
                        mDictDB.Add(strID, db);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "BaseDB.Create");
            }

            return db;
        }

        public static void LogException(Exception ex, string funcTitile = "")
        {
            MyLog.LogException(ex.ToString(), funcTitile, "Backup");
        }

        public static void LogBackup(string msg, string dirBackup = "")
        {
            MyLog.LogFile(string.Format("{0} \t {1}", msg, dirBackup), "Backup");
        }

        #endregion  

        public virtual string DBName { get; } = "iOS.db";

        protected SQLiteConnection mConn = null;
        protected string mTempFolder = "";
        protected string mDbPathOnBackupFolder = "";
        protected string mDbPathOnTempFolder = "";

        public BaseDB(string strDBFilePath)
        {
            try
            {
                this.mDbPathOnBackupFolder = strDBFilePath;

                this.mTempFolder = Folder.GetTempFilePath();
                Folder.CheckFolder(this.mTempFolder);

                string dbName = this.DBName;
                string dbNameShm = this.DBName + "-shm";
                string dbNameWal = this.DBName + "-wal";

                this.mDbPathOnTempFolder = Path.Combine(this.mTempFolder, dbName);

                string pathShmOnTemp = Path.Combine(this.mTempFolder, dbNameShm);
                string pathWalOnTemp = Path.Combine(this.mTempFolder, dbNameWal);

                string pathShmOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameShm);
                string pathWalOnBackup = this.mDbPathOnBackupFolder.Replace(dbName, dbNameWal);


                if (File.Exists(this.mDbPathOnTempFolder))
                    File.Delete(this.mDbPathOnTempFolder);

                if (File.Exists(this.mDbPathOnBackupFolder))
                    File.Copy(this.mDbPathOnBackupFolder, this.mDbPathOnTempFolder);

                if (File.Exists(pathShmOnTemp))
                    File.Delete(pathShmOnTemp);

                if (File.Exists(pathShmOnBackup))
                    File.Copy(pathShmOnBackup, pathShmOnTemp);

                if (File.Exists(pathWalOnTemp))
                    File.Delete(pathWalOnTemp);

                if (File.Exists(pathWalOnBackup))
                    File.Copy(pathWalOnBackup, pathWalOnTemp);

            }
            catch (Exception ex)
            {
                LogException(ex, "BaseDB.New");
            }
        }

        public virtual bool Open()
        {
            bool blnResult = false;

            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                {
                    blnResult = true;
                    goto DoExit;
                }

                if (File.Exists(this.mDbPathOnTempFolder))
                {
                    this.mConn = SQLiteClass3.CreateConnectionFromFile(this.mDbPathOnTempFolder);
                    if (this.mConn != null)
                    {
                        blnResult = true;
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "BaseDB.Open");
            }

        DoExit:
            return blnResult;
        }

        public virtual void Close()
        {
            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                    this.mConn.Close();
            }
            catch
            { }
        }

        public virtual void Dispose()
        {
            try
            {
                this.Close();
                GC.SuppressFinalize(this);
            }
            catch (Exception ex)
            {
                LogException(ex, "dbSMS.Dispose");
            }
        }
    }
}
