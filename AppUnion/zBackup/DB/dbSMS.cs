﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;
using System.Text;


using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

#if MAC
using Image = AppKit.NSImage;
using SQLiteFunction = Mono.Data.Sqlite.SqliteFunction;
using SQLiteFunctionAttribute = Mono.Data.Sqlite.SqliteFunctionAttribute;
using FunctionType = Mono.Data.Sqlite.FunctionType;
#else
using System.Drawing;
using System.Data.SQLite;
using SQLiteConnection = System.Data.SQLite3.SQLiteConnection;
#endif

namespace iTong.CoreModule
{
    public class dbSMSRange
    {
        public string text = string.Empty;
        public List<int> range = new List<int>();
    }

    public class dbSMSMessagePost : SMSMessage
    {
        public string service = string.Empty;

        public List<dbSMSRange> ListKeywords = new List<dbSMSRange>();
    }

    public class dbSMS : BaseDB
    {
#if MAC
        [SQLiteFunction(Arguments = 1, FuncType = Mono.Data.Sqlite.FunctionType.Scalar, Name = "read")]
#else
        [SQLiteFunction(Arguments = 1, FuncType = System.Data.SQLite.FunctionType.Scalar, Name = "read")]
#endif
        private class Read : SQLiteFunction
        {

        }

        private void RegFunc()
        {
            SQLiteFunction.RegisterFunction(typeof(Read));
        }

        public dbSMS(string strDBFilePath) : base(strDBFilePath)
        {

        }

        public override string DBName => "sms.db";

        public override bool Open()
        {
            bool blnResult = base.Open();

            if (blnResult)
                this.RegFunc();

            return blnResult;
        }

        public long GetLastMessageTime()
        {
            long lastMessageTime = 0;

            try
            {
                if (!this.Open())
                {
                    LogBackup("GetMessage.Open db failed.");
                    goto DoExit;
                }

                string strSQL = "SELECT max(date) as date FROM message";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);

                if (dt == null)
                {
                    LogBackup("GetLastMessageTime.ExecuteSQL -> dt = null");
                    goto DoExit;
                }

                if (dt.Rows.Count == 0)
                {
                    LogBackup("GetLastMessageTime.ExecuteSQL -> dt.Rows.Count = 0");
                    goto DoExit;
                }

                double timeZoneOffset = DateTime.Now.Subtract(DateTime.UtcNow).TotalHours;
                foreach (DataRow row in dt.Rows)
                {
                    lastMessageTime = Common.GetValue<long>(row["date"], 0);
                    break;
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "dbSMS.GetLastMessageTime");
            }

        DoExit:
            return lastMessageTime;
        }
              
        public List<dbSMSMessagePost> GetMessages(List<string> listKeywords = null, long lastMsgUtcTime = 0)
        {
            List<dbSMSMessagePost> listInfo = new List<dbSMSMessagePost>();

            try
            {
                if (!this.Open())
                {
                    LogBackup("GetMessage.Open db failed.");
                    goto DoExit;
                }

                string strSQLFormat = @"
SELECT 
    C.chat_identifier,
    A.country,
    A.text,
    A.date,
    A.is_sent,
    A.is_read,
    A.is_from_me,
    A.service,
    A.cache_has_attachments 
FROM 
    message A  
LEFT JOIN 
    chat_message_join B
    ON A.ROWID = B.message_id 
LEFT JOIN 
    chat C  
    ON C.ROWID = B.chat_id 
WHERE 
    A.text IS NOT NULL {0}
ORDER BY 
    C.chat_identifier ASC,
    A.date ASC;
";

                string sqlWhere = string.Empty;
                if (lastMsgUtcTime > 0)
                    sqlWhere = string.Format(" and date > {0}", lastMsgUtcTime);

                string strSQL = string.Format(strSQLFormat, sqlWhere);
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
                if (dt == null)
                {
                    LogBackup("GetMessage.ExecuteSQL -> dt = null");
                    goto DoExit;
                }

                if (dt.Rows.Count == 0)
                {
                    LogBackup("GetMessage.ExecuteSQL -> dt.Rows.Count = 0");
                    goto DoExit;
                }

                double timeZoneOffset = DateTime.Now.Subtract(DateTime.UtcNow).TotalHours;
                DateTime dt2001 = DateTime.Parse("2001-01-01");
                foreach (DataRow row in dt.Rows)
                {
                    dbSMSMessagePost smsInfo = new dbSMSMessagePost();
                    smsInfo.Country = Common.GetValue<string>(row["country"], string.Empty);
                    smsInfo.PhoneNumber = Common.GetValue<string>(row["chat_identifier"], string.Empty);
                    smsInfo.service = Common.GetValue<string>(row["service"], string.Empty);

                    if (row["date"] != null && !(row["date"] is System.DBNull))
                    {
                        long longDate = 0;
                        string dateStr = System.Convert.ToString(row["date"]);
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            int index = dateStr.IndexOf(".");
                            if (index > -1)
                                dateStr = dateStr.Substring(0, index);

                            // 兼容ios11短信时间
                            dateStr = dateStr.Substring(0, 9);

                            if (long.TryParse(dateStr, out longDate))
                                smsInfo.CreateDate = Common.ConvertToPcTime(longDate, dt2001).AddHours(timeZoneOffset);
                        }
                    }

                    smsInfo.Content = Common.GetValue<string>(row["text"], string.Empty).Trim(this.GetSpecialChat());
                    smsInfo.IsSend = (Common.GetValue<long>(row["is_sent"], 0) == 1);
                    smsInfo.Read = (Common.GetValue<long>(row["is_read"], 0) == 1);

                    if (smsInfo.IsSend == true)
                        smsInfo.IsSend = (Common.GetValue<long>(row["is_from_me"], 0) == 1);

                    if (Common.GetValue<long>(row["cache_has_attachments"], 0) == 1)
                    {
                       //是否有附件，一般是彩信
                    }

                    if (listKeywords == null)
                    {
                        listInfo.Add(smsInfo);
                    }
                    else
                    {
                        foreach (string itemKey in listKeywords)
                        {
                            int index = smsInfo.Content.IndexOf(itemKey);
                            if (index >= 0)
                            {
                                dbSMSRange keyword = new dbSMSRange() { text = itemKey };
                                keyword.range.Add(index);
                                keyword.range.Add(index + itemKey.Length);

                                smsInfo.ListKeywords.Add(keyword);
                            }
                        }

                        if (smsInfo.ListKeywords.Count > 0)
                            listInfo.Add(smsInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                LogException(ex, "dbSMS.GetMessage");
            }

        DoExit:
            return listInfo;
        }

        private char[] GetSpecialChat()
        {
            byte[] bytess = new byte[] { 239, 191, 188 };
            return System.Text.Encoding.UTF8.GetString(bytess).ToCharArray();
        }

    }
}
