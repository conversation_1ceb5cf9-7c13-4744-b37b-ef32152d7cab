﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;
using System.Threading;
using System.Diagnostics;

using iTong.CoreFoundation;
using iTong.Device;

#if MAC
using Image = AppKit.NSImage;
using Size = CoreGraphics.CGSize;
#else 
using System.Drawing;
#endif

namespace iTong.CoreModule
{
    public class dbAddressBook : BaseDB
    {
        public dbAddressBook(string strDBFilePath) : base(strDBFilePath)
        {

        }

        public override string DBName => "AddressBook.sqlitedb";

        public List<ContactGroup> GetGroups()
        {
            List<ContactGroup> listInfo = new List<ContactGroup>();

            Dictionary<string, ContactGroup> dictInfo = new Dictionary<string, ContactGroup>();
            try
            {
                if (!this.Open())
                {
                    LogBackup("GetGroups.Open db failed.");
                    goto DoExit;
                }

                string strSQL = "SELECT A.ROWID,A.Name,B.member_id FROM ABGroup A Left Join ABGroupMembers B ON A.ROWID=B.group_id ";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);

                if (dt == null)
                {
                    LogBackup("GetGroups.ExecuteSQL -> dt = null");
                    goto DoExit;
                }

                if (dt.Rows.Count == 0)
                {
                    LogBackup("GetGroups.ExecuteSQL -> dt.Rows.Count = 0");
                    goto DoExit;
                }

                foreach (DataRow row in dt.Rows)
                {
                    string strGroupID = Common.GetValue<string>(row["ROWID"], string.Empty);
                    string strGroupName = Common.GetValue<string>(row["Name"], string.Empty);
                    string strMember = Common.GetValue<string>(row["member_id"], string.Empty);

                    ContactGroup info = null;
                    if (!dictInfo.ContainsKey(strGroupID))
                    {
                        info = new ContactGroup();
                        info.ID = strGroupID;
                        info.Label = strGroupName;
                        info.Name = strGroupName;
                        info.Members = new Dictionary<string, SubItem>();
                        dictInfo.Add(strGroupID, info);
                    }
                    else
                    {
                        info = dictInfo[strGroupID];
                    }

                    info.Members[strMember] = new SubItem(strMember);
                }

                if (dictInfo.Count > 0)
                    listInfo.AddRange(dictInfo.Values);

                dictInfo.Clear();
                dictInfo = null;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbAddressBook.GetGroups");
            }

        DoExit:
            this.Close();

            return listInfo;
        }

        public List<Contact> GetContacts(List<string> listPhoneNumber = null)
        {
            List<Contact> listInfo = new List<Contact>();

            Dictionary<string, Contact> dictInfo = new Dictionary<string, Contact>();
            try
            {
                if (!this.Open())
                {
                    LogBackup("GetContacts.Open db failed.");
                    goto DoExit;
                }

                string strSQL = "SELECT * FROM ABPerson ";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);

                if (dt == null)
                {
                    LogBackup("GetContacts.ExecuteSQL -> dt = null");
                    goto DoExit;
                }

                if (dt.Rows.Count == 0)
                {
                    LogBackup("GetContacts.ExecuteSQL -> dt.Rows.Count = 0");
                    goto DoExit;
                }

                foreach (DataRow row in dt.Rows)
                {
                    string strID = Common.GetValue<string>(row["ROWID"], string.Empty).Trim();
                    if (string.IsNullOrEmpty(strID))
                        continue;

                    // 添加基本信息
                    Contact info;
                    if (dictInfo.ContainsKey(strID))
                    {
                        info = dictInfo[strID];
                    }
                    else 
                    {
                        info = new Contact();
                        info.Identifier = strID;
                        info.FirstName = Common.GetValue<string>(row["First"], string.Empty);
                        info.FirstNamePinyin = Common.GetValue<string>(row["FirstPhonetic"], string.Empty);
                        info.LastName = Common.GetValue<string>(row["Last"], string.Empty);
                        info.LastNamePinyin = Common.GetValue<string>(row["LastPhonetic"], string.Empty);
                        info.MiddleName = Common.GetValue<string>(row["Middle"], string.Empty);
                        info.Notes = Common.GetValue<string>(row["Note"], string.Empty);
                        info.Prefix = Common.GetValue<string>(row["Prefix"], string.Empty);
                        info.Suffix = Common.GetValue<string>(row["Suffix"], string.Empty);

                        if (row["Birthday"] != null && !(row["Birthday"] is System.DBNull))
                        {
                            long longDate = 0;
                            string dateStr = System.Convert.ToString(row["Birthday"]);
                            if (!string.IsNullOrEmpty(dateStr))
                            {
                                int index = dateStr.IndexOf(".");
                                if (index > -1)
                                {
                                    dateStr = dateStr.Substring(0, index);
                                    if (long.TryParse(dateStr, out longDate))
                                        info.Birthday = Common.ConvertToPcTime(longDate);
                                }
                            }
                        }

                        info.CompanyName = Common.GetValue<string>(row["Organization"], string.Empty);
                        info.Department = Common.GetValue<string>(row["Department"], string.Empty);
                        info.JobTitle = Common.GetValue<string>(row["JobTitle"], string.Empty);

                        dictInfo.Add(strID, info);
                    }

                }

                // 加载ABMultiValue、ABMultiValueLabel
                Dictionary<string, object> dictItem = this.LoadMulitValue(dictInfo);

                // 加载ABMultiValueEntry、ABMultiValueEntryKey
                this.LoadMultiValueEntry(dictItem);

                if (dictInfo.Count > 0)
                {
                    if (listPhoneNumber != null)
                    {
                        foreach (Contact contact in dictInfo.Values)
                        {
                            string phoneNumberFind = string.Empty;
                            foreach (ContactItem item in contact.PhoneNumbers)
                            {
                                string phoneNumberReplace = item.Value.FormatPhoneNumber();
                                phoneNumberFind = listPhoneNumber.Find((x) => phoneNumberReplace.Contains(x) || x.Contains(phoneNumberReplace));
                                break;
                            }

                            if (!string.IsNullOrEmpty(phoneNumberFind))
                                listInfo.Add(contact);
                        }
                    }
                    else
                    {
                        listInfo.AddRange(dictInfo.Values);
                    }
                }

                dictInfo.Clear();
                dictInfo = null;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbAddressBook.GetContacts");
            }

        DoExit:
            this.Close();

            return listInfo;
        }

        private Dictionary<string, object> LoadMulitValue(Dictionary<string, Contact> dictInfo)
        {
            Dictionary<string, object> dictItem = new Dictionary<string, object>();

            string strSQL = "SELECT A.*,B.value labelName From ABMultiValue A Left Join ABMultiValueLabel B ON A.label=B.ROWID WHERE A.property in (3,4,12,22,23,5,13)";

            DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
            if (dt == null)
            {
                LogBackup("LoadMulitValue.ExecuteSQL -> dt = null");
                goto DoExit;
            }

            if (dt.Rows.Count == 0)
            {
                LogBackup("LoadMulitValue.ExecuteSQL -> dt.Rows.Count = 0");
                goto DoExit;
            }

            foreach (DataRow row in dt.Rows)
            {
                string record_id = Common.GetValue<string>(row["record_id"], string.Empty);
                if (!dictInfo.ContainsKey(record_id))
                    continue;

                Contact info = dictInfo[record_id];

                string strProperty = Common.GetValue<string>(row["property"], string.Empty);
                string strIdentify = Common.GetValue<string>(row["identifier"], string.Empty);
                string strLabel = Common.GetValue<string>(row["label"], string.Empty);
                string strLabelName = Common.GetValue<string>(row["labelName"], string.Empty);
                string strValue = Common.GetValue<string>(row["value"], string.Empty);
                string strID = Common.GetValue<string>(row["UID"], string.Empty);

                ContactItem item = new ContactItem();
                item.ID = strID;
                item.Key = string.Format("{0}/{1}/{2}", strProperty, info.Identifier, strIdentify);
                item.Label = strLabelName;
                item.Value = strValue;
                item.LabelType = this.LabelToTextType(strLabelName);
                switch (strProperty)
                {
                    case "3":// 为3的是电话号码
                        item.ValueFormat = strValue.FormatPhoneNumber();
                        info.PhoneNumbers.Add(item);
                        break;

                    case "4":// 4为Email
                        info.Emails.Add(item);
                        break;

                    case "12":// Dates
                        long longDate = 0;
                        string dateStr = strValue;
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            int index = dateStr.IndexOf(".");
                            if (index > -1)
                            {
                                dateStr = dateStr.Substring(0, index);
                                if (long.TryParse(dateStr, out longDate))
                                    item.Value = Common.ConvertToPcTime(longDate).ToString();
                            }
                        }

                        info.Dates.Add(item);
                        break;

                    case "22": // HomePage
                        info.URLs.Add(item);
                        break;

                    case "23": // Relateds
                        info.Relateds.Add(item);
                        break;

                    case "5":
                        AddressItem adds = new AddressItem();
                        adds.Key = string.Format("{0}/{1}/{2}", strProperty, info.Identifier, strIdentify);
                        adds.Label = strLabelName;
                        adds.LabelType = this.LabelToTextType(strLabelName);
                        adds.ID = strID;                        
                        info.Addresses.Add(adds);

                        dictItem[strID] = adds;
                        break;

                    case "13":
                        IMItem im = new IMItem();
                        im.Key = string.Format("{0}/{1}/{2}", strProperty, info.Identifier, strIdentify);
                        im.Label = strLabelName;
                        im.LabelType = this.LabelToTextType(strLabelName);
                        im.ID = strID;
                        info.IMs.Add(im);

                        dictItem[strID] = im;
                        break;
                }
            }

        DoExit:
            return dictItem;
        }

        private void LoadMultiValueEntry(Dictionary<string, object> dictItem)
        {
            string strSQL = "SELECT A.*,B.value keyvalue FROM ABMultiValueEntry A Left Join ABMultiValueEntryKey B ON A.key=B.ROWID";
            DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
            
            if (dt == null)
            {
                LogBackup("LoadMultiValueEntry.ExecuteSQL -> dt = null");
                return;
            }

            if (dt.Rows.Count == 0)
            {
                LogBackup("LoadMultiValueEntry.ExecuteSQL -> dt.Rows.Count = 0");
                return;
            }

            foreach (DataRow row in dt.Rows)
            {
                string parent_id = Common.GetValue<string>(row["parent_id"], string.Empty);
                if (!dictItem.ContainsKey(parent_id))
                    continue;

                string strKeyValue = Common.GetValue<string>(row["keyvalue"], string.Empty);
                string strValue = Common.GetValue<string>(row["value"], string.Empty);

                object objItem = dictItem[parent_id];
                AddressItem adds = objItem as AddressItem;
                IMItem im = objItem as IMItem;

                switch (strKeyValue)
                {
                    case "Country":
                        if (adds != null)
                            adds.Country = strValue;
                        break;

                    case "CountryCode":
                        if (adds != null)
                            adds.CountryCode = strValue;
                        break;

                    case "State":
                        if (adds != null)
                            adds.Province = strValue;
                        break;

                    case "City":
                        if (adds != null)
                            adds.City = strValue;
                        break;

                    case "Street":
                        if (adds != null)
                            adds.Street = strValue;
                        break;

                    case "ZIP":
                        if (adds != null)
                            adds.PostalCode = strValue;
                        break;

                    case "service":
                        if (im != null)
                            im.Label = strValue;
                        break;

                    case "username":
                        if (im != null)
                            im.Value = strValue;
                        break;
                }
            }
        }

        public LabelType LabelToTextType(string strValue)
        {
            LabelType type = LabelType.Other;

            switch (strValue)
            {
                case "_$!<Mobile>!$_":
                    {
                        type = LabelType.Mobile;
                        break;
                    }

                case "iPhone":
                    {
                        type = LabelType.iPhone;
                        break;
                    }

                case "_$!<Other>!$_":
                    {
                        type = LabelType.Other;
                        break;
                    }

                case "_$!<Work>!$_":
                    {
                        type = LabelType.Work;
                        break;
                    }

                case "_$!<Home>!$_":
                    {
                        type = LabelType.Home;
                        break;
                    }

                case "_$!<HomePage>!$_":
                    {
                        type = LabelType.HomePage;
                        break;
                    }

                case "_$!<Main>!$_":
                    {
                        type = LabelType.Main;
                        break;
                    }

                case "_$!HomePage!$_":
                    {
                        type = LabelType.HomePage;
                        break;
                    }

                case "_$!Other!$_":
                    {
                        type = LabelType.Other;
                        break;
                    }

                case "_$!<HomeFAX>!$_":
                    {
                        type = LabelType.HomeFax;
                        break;
                    }

                case "_$!<Parent>!$_":
                    {
                        type = LabelType.Parent;
                        break;
                    }

                case "_$!<Anniversary>!$_":
                    {
                        type = LabelType.Anniversary;
                        break;
                    }

                case "_$!<WorkFAX>!$_":
                    {
                        type = LabelType.WorkFax;
                        break;
                    }

                case "_$!<Pager>!$_":
                    {
                        type = LabelType.Pager;
                        break;
                    }
            }

            return type;
        }
    }
}
