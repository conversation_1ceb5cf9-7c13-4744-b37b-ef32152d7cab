﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;

namespace iTong.CoreModule
{
    public enum dbCallInOut
    {
        CallIn,
        CallOut
    }

    public class dbCallHistoryRecord
    {
        public string CallHistoryROWID = "";
        public string PhoneNumber = "";
        public string Name = "";
        public DateTime CreateDate;
        public int Duration = 0;
        // Public CallFlags As Integer = 0
        public dbCallInOut CallInOut = dbCallInOut.CallIn;
        public int CountryCode = 460;
        public bool IsFind = false;
    }

    public class dbCallCallHistoryRecordPost : dbCallHistoryRecord
    {
        
    }

    public class dbCallHistory : BaseDB
    {
        public dbCallHistory(string strDBFilePath) : base(strDBFilePath)
        {

        }

        public override string DBName => "call_history.db";


        public List<dbCallCallHistoryRecordPost> GetCallHistorys(List<string> listPhoneNumber = null, int duration = 0)
        {
            List<dbCallCallHistoryRecordPost> listInfo = new List<dbCallCallHistoryRecordPost>();

            try
            {
                if (!this.Open())
                {
                    LogBackup("GetCallHistorys.Open db failed.");
                    goto DoExit;
                }

                string strSQL = "SELECT Z_PK as ROWID,ZADDRESS as address,ZDATE as date,ZDURATION as duration,ZORIGINATED as flags,ZISO_COUNTRY_CODE as country_code FROM ZCALLRECORD ORDER BY date desc";

                DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, this.mConn);
                if (dt == null)
                {
                    LogBackup("GetCallHistorys.ExecuteSQL -> dt = null");
                    goto DoExit;
                }

                if (dt.Rows.Count == 0)
                {
                    LogBackup("GetCallHistorys.ExecuteSQL -> dt.Rows.Count = 0");
                    goto DoExit;
                }

                double timeZoneOffset = DateTime.Now.Subtract(DateTime.UtcNow).TotalHours;
                DateTime dt2001 = DateTime.Parse("2001-01-01");
                foreach (DataRow row in dt.Rows)
                {
                    dbCallCallHistoryRecordPost callInfo = new dbCallCallHistoryRecordPost();
                    callInfo.CallHistoryROWID = Common.GetValue<string>(row["ROWID"], string.Empty);
                    if (row["address"] is byte[])
                    {
                        byte[] bufferAddress = Common.GetValue<byte[]>(row["address"], null);
                        if (bufferAddress != null)
                            callInfo.PhoneNumber = System.Text.Encoding.Default.GetString(bufferAddress);
                    }
                    else
                    {
                        callInfo.PhoneNumber = Common.GetValue<string>(row["address"], string.Empty);
                    }

                    if (row["date"] != null && !(row["date"] is System.DBNull))
                    {
                        long longDate = 0;
                        string dateStr = System.Convert.ToString(row["date"]);
                        if (!string.IsNullOrEmpty(dateStr))
                        {
                            int index = dateStr.IndexOf(".");
                            if (index > -1)
                                dateStr = dateStr.Substring(0, index);

                            if (long.TryParse(dateStr, out longDate))
                                callInfo.CreateDate = Common.ConvertToPcTime(longDate, dt2001).AddHours(timeZoneOffset);
                        }
                    }

                    callInfo.Duration = Common.GetValue<int>(row["duration"], 0);

                    int intValue = Common.GetValue<int>(row["flags"], 0);
                    if (intValue == 4 || intValue == 0 || intValue == 1769472)
                        callInfo.CallInOut = dbCallInOut.CallIn;
                    else
                        callInfo.CallInOut = dbCallInOut.CallOut;

                    callInfo.CountryCode = Common.GetValue<int>(row["country_code"], 0);

                    if (listPhoneNumber != null || duration > 0)
                    {
                        if (duration > 0 && callInfo.Duration > duration)
                        {
                            listInfo.Add(callInfo);
                        }
                        else
                        {
                            string phoneNumberReplace = callInfo.PhoneNumber.FormatPhoneNumber();
                            string phoneNumberFind = listPhoneNumber.Find((x) => phoneNumberReplace.Contains(x) || x.Contains(phoneNumberReplace));
                            if (!string.IsNullOrEmpty(phoneNumberFind))
                                listInfo.Add(callInfo);
                        }
                    }
                    else
                    {
                        listInfo.Add(callInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dbCallHistory.GetCallHistorys");
            }

        DoExit:
            this.Close();

            return listInfo;
        }
    }


}
