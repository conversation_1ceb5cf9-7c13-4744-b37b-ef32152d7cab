﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Data;
using System.Data.SQLite3;

using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

namespace iTong.CoreModule
{
    public static class BackupExtend
    {
        public static string GetPathOnPC(this MBFileSearchResult result, string fileNameOnPhone)
        {
            string strPath = string.Empty;

            if (result != null)
            {
                foreach (MBFileRecord record in result.ListData)
                {
                    if (record.PathOnPhone.EndsWith(fileNameOnPhone))
                    {
                        strPath = record.PathOnPhone;
                        break;
                    }
                }
            }

            return strPath;
        }
    }

    public class BackupKeyword
    {
        public List<string> ListCallHistory = new List<string>();

        public List<string> ListSMS = new List<string>();

        public List<string> ListContact = new List<string>();

        public List<string> ListNote = new List<string>();
    }

    public class BackupKeywordResult
    {
        public List<dbCallCallHistoryRecordPost> ListCallHistory = new List<dbCallCallHistoryRecordPost>();

        public List<dbSMSMessagePost> ListSMS = new List<dbSMSMessagePost>();

        public List<Contact> ListContact = new List<Contact>();

        public List<Note> ListNote = new List<Note>();

        public MBErrorType Type = MBErrorType.None;

        public string ErrMsg { get; set; } = string.Empty;

        public bool IsOK(MBFileSearchResult result)
        {
            if (result.Type == MBErrorType.None)
                return true;

            this.Type = result.Type;
            this.ErrMsg = result.ErrMsg;

            return false;
        }

    }

    public class BackupKeywordSms
    {
        
    }

    public partial class BackupMgr
    {
        private static object mLocker = new object();

        public static void LogBackup(string msg, string dirBackup = "")
        {
            MyLog.LogFile(string.Format("{0} \t {1}", msg, dirBackup), "Backup");
        }

        public static BackupKeywordResult FilterData(string dirBackup, string password, BackupKeyword keyword)
        {
            BackupKeywordResult keywordResult = new BackupKeywordResult();

            lock (mLocker)
            {
                MBFileSearchResult searchResult = null;
                MBFileSearch search = null;

                List<Contact> listContact = new List<Contact>();

                try
                {
                    /*
                    cd6702cea29fe89cf280a76794405adb17f9a0ee	HomeDomain	Library/AddressBook/AddressBookImages.sqlitedb
                    31bb7ba8914766d4ba40d6dfb6113c8b614be442	HomeDomain	Library/AddressBook/AddressBook.sqlitedb
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/AddressBook/AddressBook" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);

                    if (keywordResult.IsOK(searchResult))
                    {
                        if (searchResult.ListData.Count > 0)
                        {
                            string dbPath = searchResult.GetPathOnPC("AddressBook.sqlitedb");
                            string dbImage = searchResult.GetPathOnPC("AddressBookImages.sqlitedb");

                            dbAddressBook db = dbAddressBook.Create<dbAddressBook>(dbPath);

                            //获取全部有联系人列表
                            listContact = db.GetContacts();

                            //获取需要过滤的联系人列表
                            keywordResult.ListContact = db.GetContacts(keyword.ListContact);
                        }
                    }

                    /*
                     3d0d7e5fb2ce288813306e4d4636395e047a3d28	HomeDomain	Library/SMS/sms.db
                     */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/SMS/sms.db" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.IsOK(searchResult))
                    {
                        if (searchResult.ListData.Count > 0)
                        {
                            string dbPath = searchResult.GetPathOnPC("sms.db");

                            dbSMS db = dbSMS.Create<dbSMS>(dbPath);
                            keywordResult.ListSMS = db.GetMessages(keyword.ListSMS);
                        }
                    }

                    /*
                    5a4935c78a5255723f707230a451d79c540d2741	HomeDomain	Library/CallHistoryDB/CallHistory.storedata
                    a3388bc3048d99c38f0f6c485177ebcc64de0514	HomeDomain	Library/CallHistoryDB/com.apple.callhistory.databaseInfo.plist
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/CallHistoryDB/CallHistory" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.IsOK(searchResult))
                    {
                        if (searchResult.ListData.Count > 0)
                        {
                            string dbPath = searchResult.GetPathOnPC("CallHistory.storedata");

                            dbCallHistory db = dbCallHistory.Create<dbCallHistory>(dbPath);
                            keywordResult.ListCallHistory = db.GetCallHistorys(keyword.ListCallHistory);
                        }
                    }

                    /*
                    ca3bc056d4da0bbf88b5fb3be254f3b7147e639c	HomeDomain	Library/Notes/notes.sqlite
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/Notes/notes.sqlite" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.IsOK(searchResult))
                    {
                        if (searchResult.ListData.Count > 0)
                        {
                            string dbPath = searchResult.GetPathOnPC("notes.sqlite");

                            dbNotes db = dbNotes.Create<dbNotes>(dbPath);
                            keywordResult.ListNote = db.GetNotes(keyword.ListSMS);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "BackupMgr.Load");
                }
            }

        DoExit:
            return keywordResult;
        }

        public static BackupKeywordResult FilterSms(string dirBackup, string password, List<string> listKeyword, long lastMsgUtcTime)
        {
            BackupKeywordResult keywordResult = new BackupKeywordResult();

            lock (mLocker)
            {
                MBFileSearchResult searchResult = null;
                MBFileSearch search = null;

                List<Contact> listContact = new List<Contact>();

                try
                {
                    /*
                    cd6702cea29fe89cf280a76794405adb17f9a0ee	HomeDomain	Library/AddressBook/AddressBookImages.sqlitedb
                    31bb7ba8914766d4ba40d6dfb6113c8b614be442	HomeDomain	Library/AddressBook/AddressBook.sqlitedb
                    */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/AddressBook/AddressBook" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);

                    if (keywordResult.IsOK(searchResult) && searchResult.ListData.Count > 0)
                    {
                        string dbPath = searchResult.GetPathOnPC("AddressBook.sqlitedb");
                        string dbImage = searchResult.GetPathOnPC("AddressBookImages.sqlitedb");

                        dbAddressBook db = dbAddressBook.Create<dbAddressBook>(dbPath);

                        //获取全部有联系人列表
                        listContact = db.GetContacts();

                        //获取需要过滤的联系人列表
                        keywordResult.ListContact = db.GetContacts();
                    }

                    /*
                     3d0d7e5fb2ce288813306e4d4636395e047a3d28	HomeDomain	Library/SMS/sms.db
                     */
                    search = new MBFileSearch() { Domain = MBFileDomainType.HomeDomain, LikePath = "Library/SMS/sms.db" };
                    searchResult = mbdb.FilterDataFromBackup(dirBackup, password, search);
                    if (keywordResult.IsOK(searchResult) && searchResult.ListData.Count > 0)
                    {
                        string dbPath = searchResult.GetPathOnPC("sms.db");

                        dbSMS db = dbSMS.Create<dbSMS>(dbPath);
                        keywordResult.ListSMS = db.GetMessages(listKeyword, lastMsgUtcTime);
                                               
                        foreach (dbSMSMessagePost msg in keywordResult.ListSMS)
                        {
                            string formatPhoneNumber = msg.PhoneNumber.FormatPhoneNumber();
                            Contact contact = listContact.Find((x) => x.ContainPhoneNumber(formatPhoneNumber));
                            if (contact != null)
                                msg.Name = contact.NickName;
                        }
                    }                   
                }
                catch (Exception ex)
                {
                    BaseDB.LogException(ex, "BackupMgr.FilterSms");
                }
            }

        DoExit:
            return keywordResult;
        }
    }
}
