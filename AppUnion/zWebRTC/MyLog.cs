﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Web;

using iTong.CoreFoundation;

#if MAC
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Image = AppKit.NSImage;
using Color = AppKit.NSColor;
#else
using System.Drawing;
using System.Windows.Forms;
using System.Drawing.Imaging;
#endif

namespace iTong.Android
{

    public partial class LogEventArgs : EventArgs
    {
        public LogEventArgs()
        {

        }
        private bool mIsUploadJira = false;
        public bool IsUploadJira
        {
            get { return mIsUploadJira; }
            set { mIsUploadJira = value; }
        }

        private LogState mLS = LogState.Start;
        public LogState LS
        {
            get { return mLS; }
            set { mLS = value; }
        }

        public LogEventArgs(bool b, LogState l)
        {
            this.mIsUploadJira = b;
            this.mLS = l;
        }

        public object Sender;

        public bool Result = true;
        public string Msg = string.Empty;
        public Color MsgColor = Color.Black;
        public string FileName = string.Empty;
    }

    public enum LogState
    {
        Start,
        Prepare,
        Compression,
        Upload,
        Complete
    }

    /// <summary>
    /// 用于打印日志
    /// </summary>
    public partial class MyLog
    {

        public static EventHandler<LogEventArgs> LogCallback;
        internal static EventHandler<LogEventArgs> LogCallbackWidthSender;

        private static object mLockerPrint = new object();

        private static Thread mThreadLog;
        private static object mLocker = new object();
        private static Queue<LogEventArgs> mQueueLog = new Queue<LogEventArgs>();

        private static Dictionary<string, List<KeyValuePair<string, double>>> DictLog = new Dictionary<string, List<KeyValuePair<string, double>>>();

        public static int MaxLine = 1000;
        public static string FileNameAll = "All";
        public static string FileNameRtc = "Rtc";
        public static List<string> ListFileName = new List<string>();


        private static bool mIsTestMode = false;
        /// <summary>
        /// 是否测试测试环境
        /// </summary>
        public static bool IsTestMode
        {
            get
            {
                return mIsTestMode;
            }
            set
            {
                mIsTestMode = value;
            }
        }

        private static bool mShowLog = false;

        /// <summary>
        /// 日志输出方式是明文还是密文
        /// </summary>
        public static bool ShowLog
        {
            get
            {
                return mShowLog;
            }
            set
            {
                mShowLog = value;
                ThreadMgr.ShowLog = value;
            }
        }

        public static string UserID { get; set; } = "0";

        /// <summary>
        /// 输出日志的时间是否为UTC时间还是当地时间
        /// </summary>
        public static bool UseUtcTime { get; private set; } = true;

        /// <summary>
        /// 日志输出文件的前缀名称
        /// </summary>
        public static string LogPrefix { get; set; } = string.Empty;

        /// <summary>
        /// 日志保存默认天数
        /// </summary>
        public static int KeepDay { get; set; } = 7;

        /// <summary>
        /// 上一次清理日志时间
        /// </summary>
        protected static DateTime LastClearTime { get; set; } = DateTime.MinValue;

        static MyLog()
        {
            string dirApp = Folder.AppFolder;
#if MAC
            dirApp = Folder.ApplicationDataFolder;
            IsTestMode = File.Exists(Path.Combine(dirApp, "tmp.ini")) || File.Exists(Path.Combine(dirApp, "Test.dll"));
            ShowLog = File.Exists(Path.Combine(dirApp, "Log.dll"));
#else
            IsTestMode =
                File.Exists(Path.Combine(dirApp, "tmp.ini")) ||
                File.Exists(Path.Combine(dirApp, "Test.dll")) ||
                File.Exists(Path.Combine(Folder.ApplicationDataFolder, "tmp.ini")) ||
                File.Exists(Path.Combine(Folder.ApplicationDataFolder, "Test.dll"));

            ShowLog = File.Exists(Path.Combine(dirApp, "Log.dll")) || File.Exists(Path.Combine(Folder.ApplicationDataFolder, "Log.dll"));
#endif

            UseUtcTime = (Convert.ToInt32(TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now).TotalHours) != 8);

            ServerTool.LogCallback += OnLogCallback;
            //#if SCNEW
            //            WebSocket4Net.WebSocket.LogCallback += OnLogCallback;
            //#endif
        }

        protected static void OnLogCallback(string log, string logFileName = "")
        {
            OnLogCallback(log, logFileName, false);
        }

        /// <summary>
        /// 打印日志回调(默认文件名为Server)
        /// </summary>
        /// <param name="log">需要打印的文本</param>
        /// <param name="logFileName">打印文件名</param>
        protected static void OnLogCallback(string log, string logFileName = "", bool logStack = false)
        {
            if (string.IsNullOrEmpty(logFileName))
                logFileName = "Server";

            if (logStack)
                LogStack(log, logFileName);
            else
                LogFile(log, logFileName);
        }

        /// <summary>
        /// 清除旧日志(默认保留前一周的日志)
        /// </summary>
        /// <param name="keepDay">保留前几天的日志</param>
        public static void ClearOldLog(int keepDay = 7)
        {
            ClearOldLog(Folder.LogFolder, keepDay);

            if (Folder.ExceptionFolder != Folder.LogFolder)
                ClearOldLog(Folder.ExceptionFolder, keepDay);

            LastClearTime = DateTime.Now;
        }

        /// <summary>
        /// 清除旧日志(默认保留前一周的日志)
        /// </summary>
        /// <param name="dirLog">日志目录</param>
        /// <param name="keepDay">保留前几天的日志</param>
        public static void ClearOldLog(string dirLog, int keepDay = 7)
        {
            try
            {
                if (!Directory.Exists(dirLog))
                    return;

                if (keepDay <= 0)
                    keepDay = 3;

                string logFolder = Folder.LogFolder.TrimEnd(Path.DirectorySeparatorChar);

                bool moveToDateFolder = !string.IsNullOrEmpty(LogPrefix);
                string[] arrFile = Directory.GetFiles(dirLog, "*.*", SearchOption.AllDirectories);
                foreach (string strFile in arrFile)
                {
                    FileInfo info = new FileInfo(strFile);
                    int tmpKeepDay = keepDay;

                    string strName = info.Name;

                    if (strName.Contains("TCP") ||
                        strName.Contains("Thread") ||
                        strName.Contains("Reset") ||
                        strName.Contains("Timer") ||
                        strName.Contains("Fps"))
                    {
                        tmpKeepDay = 0;
                    }
                    else if (strName.Contains("Exception"))
                    {
                        tmpKeepDay = 30;
                    }

                    DateTime dtNow = DateTime.Now.Date;
                    if (info.LastWriteTime.AddDays(tmpKeepDay).Date < dtNow || info.CreationTime.AddDays(tmpKeepDay).Date < dtNow)
                    {
                        try
                        {
                            Console.WriteLine(info.FullName);
                            File.Delete(strFile);

                            continue;
                        }
                        catch
                        { }
                    }

                    if (info.LastWriteTime >= DateTime.Now.Date)
                        continue;

                    if (moveToDateFolder && info.DirectoryName == logFolder)
                    {
                        string dirDate = Path.Combine(info.DirectoryName, info.LastWriteTime.ToString("yyyyMMdd"));
                        try
                        {
                            if (!Directory.Exists(dirDate))
                                Directory.CreateDirectory(dirDate);

                            File.Move(strFile, Path.Combine(dirDate, info.Name));
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.ToString());
                        }
                    }
                }

                //删除空文件夹
                foreach (string itemDir in Directory.GetDirectories(dirLog))
                {
                    try
                    {
                        string[] arrItemFiles = Directory.GetFiles(itemDir, "*.*", SearchOption.AllDirectories);
                        if (arrItemFiles.Length == 0)
                        {
                            DateTime dtParse = DateTime.Now;
                            string strDate = Path.GetFileName(itemDir);
                            if (DateTime.TryParseExact(strDate, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out dtParse))
                                Directory.Delete(itemDir, true);
                        }
                    }
                    catch
                    { }
                }

                //清除剪贴板图片缓存
                string strCacheDir = Path.Combine(Folder.AppDataFolderCommon, "Cache", "ClipboardImage");
                Folder.ClearFolder(strCacheDir);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        private static LogEventArgs GetOrSetLog(LogEventArgs e, bool isClear = false)
        {
            LogEventArgs log = null;

            lock (mLocker)
            {
                if (isClear)
                {
                    mQueueLog.Clear();
                }
                else
                {
                    if (e == null)
                    {
                        if (mQueueLog.Count > 0)
                            log = mQueueLog.Dequeue();
                    }
                    else
                    {
                        mQueueLog.Enqueue(e);
                    }
                }
            }

            return log;
        }

        private static void PrintLog(LogEventArgs e)
        {
            lock (mLockerPrint)
            {
                GetOrSetLog(e);

                if (ThreadMgr.CheckThreadIsAlive(mThreadLog))
                    return;

                mThreadLog = ThreadMgr.StartWithPara(PrintLogThread);
            }
        }

        private static void PrintLogThread(object state)
        {
            try
            {
                //int tryCount = 0;
                while (true)
                {
                    if (DateTime.Now.Subtract(LastClearTime).TotalDays > 1)
                        ClearOldLog(KeepDay);

                    LogEventArgs log = GetOrSetLog(null);
                    if (log == null)
                    {
                        //tryCount++;

                        ////连接10秒消息结束线程
                        //if (tryCount > 1000)
                        //    break;

                        Thread.Sleep(10);
                        continue;
                    }

                    ////有消息，重置计数器
                    //tryCount = 0;

                    if (log.Sender != null)
                        LogCallbackWidthSender?.Invoke(log.Sender, log);
                    else
                        LogCallback?.Invoke(log.Sender, log);
                }
            }
            catch
            { }
            finally
            {
                mThreadLog = null;
            }
        }

        /// <summary>
        /// 退出日志打印
        /// </summary>
        public static void Exit()
        {
            GetOrSetLog(null, true);

            ThreadMgr.Abort(mThreadLog);
            mThreadLog = null;

            Common.LogExit();
        }

        /// <summary>
        /// 打印日志到目标文件
        /// </summary>
        /// <param name="msg">需要打印的文本</param>
        /// <param name="strPreview">打印文件名</param>
        /// <param name="showInRichTextBox"></param>
        /// <param name="frameIndex"></param>
        public static void LogFile(string msg, string strPreview, bool showInRichTextBox = true, int frameIndex = -1)
        {
            Log(msg, string.Empty, showInRichTextBox, Color.Black, strPreview, false, null, false, frameIndex);
        }

        public static void LogStack(string msg, string strPreview = "Stack", bool printStack = true)
        {
            Log(msg, "", true, Color.Black, strPreview, false, null, printStack);
        }

        public static void Log(string msg, string className = "", bool showInRichTextBox = true)
        {
            Log(msg, className, showInRichTextBox, Color.Black, string.Empty);
        }

        /// <summary>
        /// 打印日志
        /// </summary>
        /// <param name="msg">需要打印的文本</param>
        /// <param name="className">类别名称</param>
        /// <param name="showInRichTextBox">是否富文本显示</param>
        /// <param name="msgColor">文本颜色</param>
        /// <param name="strPreview">打印文件</param>
        /// <param name="blnResult"></param>
        /// <param name="sender"></param>
        /// <param name="printStack">是否打印堆栈</param>
        /// <param name="frameIndex">堆栈帧索引</param>
        public static void Log(string msg, string className, bool showInRichTextBox, Color msgColor, string strPreview = "", bool blnResult = false, object sender = null, bool printStack = false, int frameIndex = -1)
        {
            try
            {
                StackTrace st = null;

                if (printStack) // 将堆栈信息拿取出来
                {
                    st = new StackTrace();

                    StringBuilder sbStack = new StringBuilder();
                    sbStack.AppendLine(msg);

                    for (int index = 0; index < st.FrameCount; index++)
                    {
                        StackFrame sf = st.GetFrame(index);
                        if (sf != null)
                        {
                            System.Reflection.MethodBase method = sf.GetMethod();
                            if (!method.DeclaringType.FullName.Contains(".MyLog") && !method.DeclaringType.FullName.Contains("System."))
                                sbStack.AppendLine(method.DeclaringType.FullName + "." + method.Name + GetMethodParams(method));
                        }
                    }
                    msg = sbStack.ToString();
                }

                if (string.IsNullOrEmpty(className)) // 拿取className
                {
                    if (st == null)
                        st = new StackTrace();

                    if (frameIndex == -1)
                        frameIndex = 2;

                    int indexStart = frameIndex;
                    if (indexStart >= st.FrameCount)
                        indexStart = st.FrameCount - 1;

                    StackFrame[] stackFrames = st.GetFrames();

                    //for (int index = indexStart; index < st.FrameCount; index++)
                    foreach (StackFrame sf in stackFrames)
                    {
                        //StackFrame sf = st.GetFrame(index);
                        if (sf != null)
                        {
                            System.Reflection.MethodBase method = sf.GetMethod();
                            if (method.DeclaringType.FullName == typeof(MyLog).FullName)
                                continue;

#if !DEBUG
                            if (method.IsPrivate)
                            {
                                className = method.DeclaringType.Name;
                            }
                            else
#endif
                            {
                                className = method.DeclaringType.Name + "." + method.Name;
                            }
                        }
                        break;
                    }
                }

                if (string.IsNullOrEmpty(strPreview))
                    strPreview = FileNameRtc;

                if (!ListFileName.Contains(strPreview))
                    ListFileName.Add(strPreview);

                string strMsg = string.Format("[{0}] >>> {1}", className, msg);
                string strPrint = string.Empty;

                string logDir = Folder.LogFolder;

                DateTime dt = (UseUtcTime ? DateTime.UtcNow : DateTime.Now);
                if (IsTestMode || ShowLog)
                {
                    string strFile = string.Format("{0}{1}", strPreview, dt.ToString("yyyyMMdd"));

                    strPrint = string.Format("{0} {1}", dt.ToString("yyyy-MM-dd HH:mm:ss.ffff"), strMsg);

                    Common.Log(strPrint, strFile, true, logDir, true, false, false);
                }
                else
                {
                    string strFile = string.Format("{0}{1}_des", strPreview, dt.ToString("yyyyMMdd"));

                    strMsg = EncryptString(strMsg);


                    JsonObject dictStatItem = new JsonObject();

                    dictStatItem.Add("content", strMsg);
                    dictStatItem.Add("model", 0);
                    dictStatItem.Add("level", 2);
                    dictStatItem.Add("function_name", "");
                    dictStatItem.Add("account_id", UserID);
                    dictStatItem.Add("app_ver", Common.GetSoftVersion());
                    dictStatItem.Add("event_time", GetNowTimestampSeconds());

                    strMsg = JsonParser.SaveString(dictStatItem);

                    strPrint = string.Format("{0} {1}", dt.ToString("yyyy-MM-dd HH:mm:ss.ffff"), strMsg);//None Debug 
                    Common.Log(strPrint, strFile, true, logDir, true, false, false);
                }

                if (showInRichTextBox)
                    PrintLog(new LogEventArgs() { FileName = strPreview, Msg = msg, MsgColor = msgColor, Result = blnResult, Sender = sender });
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        /// <summary>
        /// 打印日志到dll日志,注意：这是同步打印的
        /// </summary>
        /// <param name="msg">需要打印的文本</param>
        /// <param name="fileName">文件名</param>
        /// <param name="printStack">是否打印堆栈</param>
        public static void WriteLine(string msg, string fileName = "Dll", bool printStack = false, string extension = ".txt")
        {
            try
            {
                string strDir = Folder.LogFolder;

                string strPrefix = LogPrefix; // 分隔符_来着

                if (!string.IsNullOrEmpty(strPrefix))
                    strPrefix += "_";

                string strPath = Path.Combine(strDir, string.Format("{0}{1}{2}{3}", strPrefix, fileName, DateTime.Now.ToString("yyyyMMdd"), extension));

                Folder.CheckFolder(strDir);

                if (printStack)// 拿取堆栈信息
                {
                    StackTrace st = new StackTrace();

                    StringBuilder sbStack = new StringBuilder();
                    sbStack.AppendLine(msg);

                    for (int index = 0; index < st.FrameCount; index++)
                    {
                        StackFrame sf = st.GetFrame(index);
                        if (sf != null)
                        {
                            System.Reflection.MethodBase method = sf.GetMethod();
                            sbStack.AppendLine(method.DeclaringType.FullName + "." + method.Name);
                        }
                    }
                    msg = sbStack.ToString();
                }

                DateTime dt = (MyLog.UseUtcTime ? DateTime.UtcNow : DateTime.Now);
                string strPrint = string.Format("{0} {1}", dt.ToString("yyyy-MM-dd HH:mm:ss.ffff"), msg);

                // 写入文本
                using (StreamWriter sw = new StreamWriter(strPath, true, Encoding.UTF8))
                    sw.WriteLine(strPrint);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        /// <summary>
        /// 加密字符串
        /// </summary>
        /// <param name="str">待加密字符串</param>
        /// <returns>加密后的字符串</returns>
        public static string EncryptString(string str)
        {
            return
                Common.ToHexString(Common.EncryptDES(Encoding.UTF8.GetBytes(str),
                    KeyContent,
                    KeyContent, CipherMode.ECB, PaddingMode.PKCS7));
        }
        
        /// <summary>
        /// 打印报错异常
        /// </summary>
        /// <param name="strLog">异常信息</param>
        /// <param name="funcTitle">发生异常的函数名</param>
        public static void LogException(string strLog, string funcTitle = "", string strPreview = "")
        {
            Common.LogException(strLog, funcTitle);

            Log(strLog, funcTitle, true, Color.Red, strPreview, printStack: true);
        }

        /// <summary>
        /// 打印报错异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="funcTitle">发生异常的函数名</param>
        public static void LogException(Exception ex, string funcTitle = "", string strPreview = "")
        {
            Common.LogException(ex, funcTitle);

            Log(ex.ToString(), funcTitle, true, Color.Red, strPreview, printStack: true);
        }

        private static byte[] KeyContent
        {
            get { return Encoding.Default.GetBytes("890jklms"); }
        }

        private static byte[] KeyFuncName
        {
            get { return Encoding.UTF8.GetBytes("+*^$@)%!"); }
        }

        /// <summary>
        /// 解密文件
        /// </summary>
        /// <param name="strFile">待解密的文件</param>
        /// <param name="strFileDecode">解密出来的临时文件</param>
        /// <returns>解密成功返回true，否则为false</returns>
        public static bool DecodeFile(string strFile, string strFileDecode = "")
        {
            bool blnResult = false;

            try
            {
                string ext = Path.GetExtension(strFile).ToLower();
                if (ext != ".log" && ext != ".txt")
                    goto DoExit;

                if (Path.GetFileNameWithoutExtension(strFile).Contains("_decode"))
                    goto DoExit;

                if (ext == ".txt" && !strFile.EndsWith("_des.txt", StringComparison.OrdinalIgnoreCase))
                    goto DoExit;

                string strKey = "{\"content\"";
                //string strKeyTime = ",\"event_time\":";

                using (StreamReader sr = new StreamReader(strFile, Encoding.UTF8))
                {
                    if (string.IsNullOrEmpty(strFileDecode))
                    {
                        string strName = string.Format("{0}_decode{1}", Path.GetFileNameWithoutExtension(strFile), Path.GetExtension(strFile));
                        string strDir = Path.GetDirectoryName(strFile);
                        strFileDecode = Path.Combine(strDir, strName);
                    }

                    using (StreamWriter sw = new StreamWriter(strFileDecode, false, Encoding.UTF8))
                    {
                        while (!sr.EndOfStream)
                        {
                            string strLine = sr.ReadLine();
                            int index = strLine.IndexOf(strKey);
                            if (index >= 0)
                            {
                                sw.Write(strLine.Substring(0, index));

                                string strJson = strLine.Substring(index);
                                JsonObject dictJson = JsonParser.ParseString(strJson);
                                if (dictJson == null || !dictJson.ContainsKey("content"))
                                {
                                    sw.WriteLine(strJson);
                                }
                                else
                                {
                                    if (dictJson.ContainsKey("function_name"))
                                    {
                                        string function_name = dictJson["function_name"].ToString();
                                        if (!string.IsNullOrEmpty(function_name))
                                        {
                                            string hexFuncName = dictJson["function_name"].ToString();
                                            byte[] encodeFuncName = Common.FromHexString(hexFuncName);
                                            byte[] arrDecodeFuncName = Common.DecryptDES(encodeFuncName, KeyFuncName, KeyFuncName, System.Security.Cryptography.CipherMode.ECB, System.Security.Cryptography.PaddingMode.PKCS7);
                                            string decodeFuncName = Encoding.UTF8.GetString(arrDecodeFuncName);

                                            if (!string.IsNullOrEmpty(decodeFuncName))
                                                sw.Write(string.Format(" FuncName: {0} ", decodeFuncName));
                                        }
                                    }

                                    string hexContent = dictJson["content"].ToString();
                                    try
                                    {
                                        byte[] encodeContent = Common.FromHexString(hexContent);
                                        byte[] arrDecode = Common.DecryptDES(encodeContent, KeyContent, KeyContent, System.Security.Cryptography.CipherMode.ECB, System.Security.Cryptography.PaddingMode.PKCS7);
                                        string decodeContent = Encoding.UTF8.GetString(arrDecode);

                                        sw.WriteLine(decodeContent);
                                    }
                                    catch
                                    {
                                        sw.WriteLine(strJson);
                                    }
                                }
                            }
                            else
                            {
                                sw.WriteLine(strLine);
                            }
                        }
                    }
                }

                blnResult = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

        DoExit:
            return blnResult;
        }

        /// <summary>
        /// 加载日志
        /// </summary>
        /// <param name="strPreview">需要加载的文件名</param>
        /// <returns></returns>
        public static string LoadFile(string strPreview)
        {
            string strLog = string.Empty;

            try
            {
                string strFile = string.Empty;
                string tmpFile = string.Empty;

                bool isEncrypt = false;

                if (MyLog.IsTestMode || MyLog.ShowLog)
                {
                    strFile = Path.Combine(Folder.LogFolder, string.Format("{0}{1}.txt", strPreview, DateTime.Now.ToString("yyyyMMdd")));
                }
                else
                {
                    strFile = Path.Combine(Folder.LogFolder, string.Format("{0}{1}_des.txt", strPreview, DateTime.Now.ToString("yyyyMMdd")));
                    isEncrypt = true;
                }

                if (!File.Exists(strFile))
                    goto DoExit;

                string[] arrLine = null;
                if (isEncrypt)
                {
                    tmpFile = Folder.GetTempFilePath();
                    DecodeFile(strFile, tmpFile);

                    if (File.Exists(tmpFile))
                        arrLine = File.ReadAllLines(tmpFile, Encoding.UTF8);
                }
                else
                {
                    arrLine = File.ReadAllLines(strFile, Encoding.UTF8);
                }

                if (arrLine != null)
                {
                    int lineStart = 0;
                    int lineCount = arrLine.Length;
                    int lineMax = MaxLine / 2;
                    if (arrLine.Length > lineMax)
                    {
                        lineCount = lineMax;
                        lineStart = arrLine.Length - lineCount;
                    }

                    strLog = string.Join("\r\n", arrLine, lineStart, lineCount);
                }

                if (isEncrypt && File.Exists(tmpFile))
                    File.Delete(tmpFile);
            }
            catch (Exception ex)
            {
                LogException(ex, "LoadFile");
            }

        DoExit:
            return strLog;
        }

        /// <summary>
        /// 通过key清除日志(并非清除文件)
        /// </summary>
        /// <param name="strKey"></param>
        public static void LogClearByKey(string strKey)
        {
            if (!DictLog.ContainsKey(strKey))
                return;

            DictLog[strKey].Clear();
        }

        /// <summary>
        /// 通过key复制日志
        /// </summary>
        /// <param name="oldKey">旧的key</param>
        /// <param name="newKey">新的key</param>
        public static void LogCopyByKey(string oldKey, string newKey)
        {
            if (string.IsNullOrEmpty(oldKey) || !DictLog.ContainsKey(oldKey) || string.Compare(oldKey, newKey, true) == 0)
                return;

            if (!DictLog.ContainsKey(newKey))
                DictLog[newKey] = new List<KeyValuePair<string, double>>();

            DictLog[newKey].Clear();
            DictLog[newKey].AddRange(DictLog[oldKey]);
        }

        /// <summary>
        /// 通过key添加日志
        /// </summary>
        /// <param name="strKey">key值</param>
        /// <param name="strLog">日志信息</param>
        /// <param name="timeTake">日志时间</param>
        public static void LogAddByKey(string strKey, string strLog, double timeTake)
        {
            if (string.IsNullOrEmpty(strKey))
                return;

            if (!DictLog.ContainsKey(strKey))
                DictLog[strKey] = new List<KeyValuePair<string, double>>();

            DictLog[strKey].Add(new KeyValuePair<string, double>(strLog, timeTake));
        }

        /// <summary>
        /// 通过key获取日志
        /// </summary>
        /// <param name="strKey">key键</param>
        /// <returns>返回日志字符串</returns>
        public static string LogGetByKey(string strKey)
        {
            string strLog = string.Empty;

            if (DictLog.ContainsKey(strKey))
            {
                List<KeyValuePair<string, double>> list = DictLog[strKey];

                double totalTime = 0;

                StringBuilder sb = new StringBuilder();
                foreach (KeyValuePair<string, double> item in list)
                {
                    sb.AppendLine(string.Format("{0}: {1}s", item.Key, item.Value.ToString("0.##")));

                    totalTime += item.Value;
                }
                sb.AppendLine(string.Format("Total Time: {0}s", totalTime.ToString("0.##")));

                strLog = sb.ToString();
            }

            return strLog;
        }

        /// <summary>
        /// 获取此时时间转化为utc毫秒时间戳
        /// </summary>
        /// <returns>此时时间距离1970.1.1协调世界时的毫秒时间戳</returns>
        public static long GetNowTimestamp()
        {
            return DateTimeToTimestamp(DateTime.Now);
        }

        /// <summary>
        /// 获取此时时间转化为utc秒时间戳
        /// </summary>
        /// <returns>此时时间距离1970.1.1协调世界时的秒时间戳</returns>
        public static long GetNowTimestampSeconds()
        {
            return DateTimeToTimestampSeconds(DateTime.Now);
        }

        /// <summary>
        /// 将毫秒时间戳转化为本地地区时间
        /// </summary>
        /// <param name="lonTimestamp">毫秒时间戳</param>
        /// <returns>返回从1970.1.1加上毫秒时间戳的本地地区时间</returns>
        public static DateTime TimestampToDateTime(long lonTimestamp)
        {
            DateTime dtStart = new DateTime(1970, 1, 1);
            DateTime dtResult = dtStart.AddMilliseconds(lonTimestamp);
            dtResult = dtResult.ToLocalTime();
            return dtResult;
        }

        /// <summary>
        /// 将秒时间戳转化为本地地区时间
        /// </summary>
        /// <param name="lonTimestamp">秒时间戳</param>
        /// <returns>返回从1970.1.1加上秒时间戳的本地地区时间</returns>
        public static DateTime TimestampToDateTimeSeconds(long lonTimestamp)
        {
            DateTime dtStart = new DateTime(1970, 1, 1);
            DateTime dtResult = dtStart.AddSeconds(lonTimestamp);
            dtResult = dtResult.ToLocalTime();
            return dtResult;
        }

        /// <summary>
        /// 将秒时间戳转化为utc时间
        /// </summary>
        /// <param name="lonTimestamp">秒时间戳</param>
        /// <returns>返回从1970.1.1加上秒时间戳的UTC时间</returns>
        public static DateTime TimestampToDateTimeSecondsUTC(long lonTimestamp)
        {
            DateTime dtStart = new DateTime(1970, 1, 1);
            DateTime dtResult = dtStart.AddSeconds(lonTimestamp);
            return dtResult;
        }

        /// <summary>
        /// 将某个时间转化为utc毫秒时间戳
        /// </summary>
        /// <param name="dtDate">时间</param>
        /// <returns>返回utc毫秒时间戳</returns>
        public static long DateTimeToTimestamp(DateTime dtDate)
        {
            TimeSpan ts = dtDate.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            long lonTimestamp = Convert.ToInt64(ts.TotalMilliseconds);
            return lonTimestamp;
        }

        /// <summary>
        /// 将某个时间转化为utc秒时间戳
        /// </summary>
        /// <param name="dtDate">时间</param>
        /// <returns>返回utc秒时间戳</returns>
        public static long DateTimeToTimestampSeconds(DateTime dtDate)
        {
            TimeSpan ts = dtDate.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            long lonTimestamp = Convert.ToInt64(ts.TotalSeconds);
            return lonTimestamp;
        }

        /// <summary>
        /// 创建或删除日志和测试文件
        /// </summary>
        public static void CreateOrRemoveLogAndTmpFile()
        {
            try
            {

#if MAC
                Utility.DeleteFile(Path.Combine(Folder.ApplicationDataFolder, "debug.dll"));
#endif

                if (IsTestMode)
                {
                    File.WriteAllText(Path.Combine(Folder.ApplicationDataFolder, "tmp.ini"), "");
                    //File.WriteAllText(Path.Combine(Folder.AppFolder, "tmp.ini"), "");
                }
                else
                {
                    Utility.DeleteFile(Path.Combine(Folder.ApplicationDataFolder, "tmp.ini"));
#if !MAC
                    Utility.DeleteFile(Path.Combine(Folder.AppFolder, "tmp.ini"));
#endif
                }

                if (ShowLog)
                {
                    File.WriteAllText(Path.Combine(Folder.ApplicationDataFolder, "Log.dll"), "");
                    //File.WriteAllText(Path.Combine(Folder.AppFolder, "Log.dll"), "");
                }
                else
                {
                    Utility.DeleteFile(Path.Combine(Folder.ApplicationDataFolder, "Log.dll"));
#if !MAC
                    Utility.DeleteFile(Path.Combine(Folder.AppFolder, "Log.dll"));
#endif
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "Mylog.CreateOrRemoveLogAndTmpFile");
            }
        }

        public static string GetMethodParams(System.Reflection.MethodBase method)
        {
            StringBuilder paramsBuilder = new StringBuilder();

            try
            {
                foreach (System.Reflection.ParameterInfo param in method.GetParameters())
                {
                    if (paramsBuilder.Length > 0)
                        paramsBuilder.Append(", ");

                    // 处理参数修饰符 (out/ref)
                    if (param.IsOut)
                        paramsBuilder.Append("out ");
                    else if (param.ParameterType.IsByRef)
                        paramsBuilder.Append("ref ");

                    // 获取实际类型（处理ByRef类型）
                    Type paramType = param.ParameterType;
                    if (paramType.IsByRef && paramType.HasElementType)
                    {
                        paramType = paramType.GetElementType();
                    }
                   
                    if (paramType.IsArray)
                    {
                        // 处理数组类型
                        paramsBuilder.Append(paramType.GetElementType().Name);
                        paramsBuilder.Append("[]");
                    }                    
                    else if (paramType.IsGenericType)
                    {
                        // 处理泛型类型
                        string name = paramType.Name.Split('`')[0];

                        List<string> list = new List<string>();
                        foreach (Type t in paramType.GetGenericArguments())
                        {
                            list.Add(t.Name);
                        }

                        paramsBuilder.Append($"{name}<{string.Join(", ", list.ToArray())}>");
                    }                    
                    else
                    {
                        // 处理普通类型
                        paramsBuilder.Append(paramType.Name);
                    }

                    // 添加参数名
                    paramsBuilder.Append(' ').Append(param.Name);

                    // 处理可选参数默认值 (.NET 4.7.1兼容写法)
                    if (param.IsOptional)
                    {
                        object defaultValue = param.DefaultValue;
                        string defaultValueStr = (defaultValue == null) ? "null" :
                                               (defaultValue is string) ? $"\"{defaultValue}\"" :
                                               defaultValue.ToString();
                        paramsBuilder.Append(" = ").Append(defaultValueStr);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

            return paramsBuilder.Length > 0 ? $"({paramsBuilder})" : "";
        }
    }
}
