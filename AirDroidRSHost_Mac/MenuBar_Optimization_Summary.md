# 左上角菜单栏优化方案

## 优化目标
1. 使用 `this.Language.GetString()` 实现完整的多语言支持
2. 只显示必要的菜单项，排除不需要的系统菜单
3. 简化菜单创建和管理逻辑

## 主要改进

### 1. 完全代码化创建菜单
**之前的问题：**
- XIB文件中硬编码英文标题
- 运行时动态查找和修改菜单项，复杂且容易出错
- 多语言支持不完整

**优化方案：**
- 移除XIB依赖，在代码中直接创建菜单
- 创建时直接使用 `this.Language.GetString()` 获取本地化文本
- 避免运行时查找和修改菜单项

### 2. 简化菜单结构
**应用菜单（Remote Support）包含：**
- About Remote Support - 关于信息
- Preferences… - 偏好设置
- Hide AirDroid Remote Support - 隐藏应用
- Hide Others - 隐藏其他应用  
- Show All - 显示所有应用
- Quit AirDroid Remote Support - 退出应用

**Window菜单包含：**
- Minimize - 最小化窗口
- Bring All to Front - 前置所有窗口

### 3. 移除复杂的菜单清理逻辑
**之前的问题：**
- 复杂的系统菜单项检测和移除逻辑
- 定时器检查菜单变化
- 多语言关键词匹配

**优化方案：**
- 完全控制菜单创建，只添加需要的菜单项
- 不需要清理不需要的菜单项

## 核心代码改进

### 新的菜单创建方法：
```csharp
private void SetupMenuHandlers()
{
    // 创建主菜单
    NSMenu mainMenu = new NSMenu();
    
    // 创建应用菜单
    this.CreateApplicationMenu(mainMenu);
    
    // 创建Window菜单
    this.CreateWindowMenu(mainMenu);
    
    // 设置为应用程序的主菜单
    NSApplication.SharedApplication.MainMenu = mainMenu;
}
```

### 多语言支持：
```csharp
// 直接在创建时使用本地化文本
NSMenuItem aboutItem = new NSMenuItem(
    this.Language.GetString("airdroid_remote_support_about"), 
    new ObjCRuntime.Selector("handleAboutMenu:"), 
    ""
);
```

## 需要的语言资源键

确保以下语言资源键在语言文件中存在：
- `airdroid_remote_support_about` - "关于 AirDroid Remote Support"
- `airdroid_remote_support_preferences` - "偏好设置..."
- `airdroid_remote_support_hide` - "隐藏 AirDroid Remote Support"
- `airdroid_remote_support_hide_others` - "隐藏其他"
- `airdroid_remote_support_show_all` - "显示全部"
- `airdroid_remote_support_quit` - "退出 AirDroid Remote Support"
- `window_menu_title` - "窗口"
- `airdroid_remote_support_minimize` - "最小化"
- `window_bring_all_to_front` - "前置所有窗口"

## 优化效果

1. **多语言支持完整** - 所有菜单项都使用本地化文本
2. **代码简化** - 移除了复杂的菜单查找和清理逻辑
3. **维护性提高** - 菜单创建逻辑集中，易于维护
4. **性能提升** - 不需要运行时查找和修改菜单项
5. **可靠性增强** - 避免了菜单项查找失败的问题

## 后续建议

1. 完全移除原始的 MainMenu.xib 文件
2. 测试所有语言环境下的菜单显示
3. 确认所有菜单项的事件处理正常工作
4. 考虑将菜单创建逻辑提取到单独的类中，进一步提高代码组织性
