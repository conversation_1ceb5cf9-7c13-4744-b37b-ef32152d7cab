using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;

using AppKit;
using Foundation;
using CoreGraphics;
using ObjCRuntime;
using iTong.Android;
using CoreFoundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;


namespace iTong.CoreModule
{
    public partial class rsMainForm
    {
        #region 属性

        public override int skTitleBarHeight => 0;
        public override int skStatusBarHeight => 40;
        public override skSplit skTransparentImageSplit => new skSplit(15);
        public bool IsConnected { get; set; }

        #endregion

        #region 私有字段

        private frmBase frmSetting;
        private frmBase frmBinding;
        private frmBase frmPerssion;
        private skButton btnTitle;
        private NSImage mNormalImage;
        private NSImage mAlternateImage;

        #endregion

        public rsMainForm()
        {
            mListMainForm.Add(this);

            ///用于判断是不是首次开机自启动
            //if (SettingMgr.GetValue(KeyNameForRS.Minimize, false))
            //    this.Visible = false;

            InitializeComponent();

            this.InitFrame();

            RsClient.Form = this;
            PageHelper.InitCef();

            // 监听设置窗口中密码更新方式变更事件，实时刷新密码标题
            rsSetting.NoticePasswordUpdateEvent += this.OnSettingPwdUpdate;
        }

        public override void OnShown(object sender, EventArgs e)
        {
            base.OnShown(sender, e);

            MyAPI.AccessPowerCallback += this.OnAccessPowerCallback;

            if (this.IsFisrtInstall() && (!MyAPI.CheckHasScreenCapture() || !MyAPI.CheckHasAccessibility()))
            {
                this.ShowPerssionsWindow(true);
            }

            //ThreadMgr.Start(new ThreadStart(() => {
            //    try
            //    {
            //        Image img = MyResource.GetImage("airdroid_48.png");
            //        using (MemoryStream ms = new MemoryStream())
            //        {
            //            img.Save(ms, NSBitmapImageFileType.Png);
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex.ToString());
            //    }
            //}));
        }

        public override void OnWindowWillClose(object sender, EventArgs e)
        {
            // 解除事件绑定，避免潜在的内存泄漏
            rsSetting.NoticePasswordUpdateEvent -= this.OnSettingPwdUpdate;
            MyAPI.AccessPowerCallback -= this.OnAccessPowerCallback;

            base.OnWindowWillClose(sender, e);
        }

        private void OnAccessPowerCallback(object sender, AccessPowerEventArg e)
        {
            this.SetPessionsTipState();
        }

        #region 权限管理

        private bool IsNeedShowPessionsTip()
        {
            int count = 0;

            bool isScreenPermission = MyAPI.AuthStatusForScreenCapture;  //是否有屏幕录制权限
            bool isAccessibilityPermission = MyAPI.AuthStatusForAccessibility; //是否有辅助功能权限
            bool isDiskPermission = MyAPI.AuthStatusForAllFiles; //是否有完全磁盘访问权限
            bool isMicrophonePermission = MyAPI.AuthStatusForMicrophone; //是否有麦克风权限

            if (!isScreenPermission)
                count += 1;

            if (!isAccessibilityPermission)
                count += 1;

            if (!isDiskPermission)
                count += 1;

            if (!isMicrophonePermission)
                count += 1;

            if (count > 0)
            {
                this.btnPermissionsCount.skBadgeNumber = count;
            }

            return count > 0;
        }

        private void SetPessionsTipState()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new Action(() => {
                    this.SetPessionsTipState();
                }));
            }
            else
            {
                if (this.IsNeedShowPessionsTip())
                {
                    skPanel panel = (this.pnlNoCompany.Visible ? this.pnlNoCompany : this.pnlCompanyName);

                    if (this.btnPermissionsCount.AccessibleDescription != panel.Name)
                    {
                        this.btnPermissionsCount.AccessibleDescription = panel.Name;

                        if (this.btnPermissionsCount.Superview != null)
                            this.btnPermissionsCount.RemoveFromSuperview();

                        panel.AddSubview(this.btnPermissionsCount);

                        if (this.pnlNoCompany.Visible)
                        {
                            this.btnPermissionsCount.Location = new Point(this.btnBinding.Left, this.btnBinding.Top - 10 - this.btnPermissionsCount.Height);
                        }
                        else
                        {
                            this.btnPermissionsCount.Location = new Point(this.pnlRSType.Left, this.pnlRSType.Top - 10 - this.btnPermissionsCount.Height);
                        }

                        this.btnPermissionsCount.BringToFront();
                    }
                }
                else
                {
                    if (this.btnPermissionsCount.Superview != null)
                        this.btnPermissionsCount.RemoveFromSuperview();
                }
            }
        }

        private void ShowPerssionsWindow(bool isFirstInstall = false)
        {
            if (this.frmPerssion != null)
            {
                this.frmPerssion.Activate();
                return;
            }

            if (this.frmPerssion == null)
            {
                this.frmPerssion = new rsPermission(isFirstInstall);
                this.frmPerssion.FormClosing += frmPerssion_FormClosing;

                if (isFirstInstall)
                    this.frmPerssion.ShowDialog(this);
                else
                    this.frmPerssion.Show(this);
            }

            return;
        }

        #endregion

        private bool IsFisrtInstall()
        {
            bool isFirstInstall = false;

            if (!SettingMgr.GetValue(KeyNameForRS.NotFirstInstall, false))
            {
                isFirstInstall = true;
            }

            return isFirstInstall;

            #region test
            //return true;
            #endregion test
        }


        protected void InitFrame()
        {
            //TimerMgr.Create(30, new ThreadStart(() => {
            //    ServiceMgr.WakeUpDisplay();
            //}));

            Size sizeForm = new Size(440, 600);
            this.Size = sizeForm;

            this.skShowStatusBar = false;
            this.skShowButtonMax = false;
            this.skShowButtonMin = true;
            this.skShowButtonClose = true;

            this.ShowInTaskbar = false;

            this.skTitle = this.Language.GetString("pc_rs_intro_title");
            this.skTitleFont = MyFont.CreateFont(9.75f);
            this.skTitleColor = skColor.FromArgb(45, 47, 51);
            this.skTitleBackgroundColor = skColor.FromArgb(245, 246, 248);
            this.skBackgroundColor = Color.White;
            this.skUseHandCursor = false;

            if (this.btnTitle == null)
            {
                this.btnTitle = new skButton();
                this.AddSubview(this.btnTitle);
            }

            this.btnTitle.Size = new Size(this.Width, 30);
            this.btnTitle.Location = new Point(0, this.Height - this.btnTitle.Height);
            this.btnTitle.Anchor = AnchorStyles.Top | AnchorStyles.Right | AnchorStyles.Left;
            this.btnTitle.skTextAlign = NSTextAlignment.Center;
            this.btnTitle.skTextFont = MyFont.CreateFont(13);
            this.btnTitle.skTextColor = this.skTitleColor;
            this.btnTitle.skText = this.skTitle;
            this.btnTitle.skBackgroundColor = this.skTitleBackgroundColor;
            this.btnTitle.skSuperviewReceiveMessage = true;
            this.btnTitle.skUseHandCursor = false;

            this.btnSetting.BringToFront();



            int diffY = (this.btnTitle.Height - this.btnSetting.Height) / 2;
            this.btnSetting.Location = new Point(this.Width - diffY - this.btnSetting.Width, this.btnTitle.Top + diffY);

            this.pnlNoCompany.Location = new Point((sizeForm.Width - this.pnlNoCompany.Width) / 2, sizeForm.Height - 60 - this.pnlNoCompany.Height);
            this.pnlCompany.Location = this.pnlNoCompany.Location;

            //this.pnlNoCompany.skBackgroundColor = Color.Red;
            //this.pnlCompany.skBackgroundColor = Color.Blue;
            //this.picDevice.skBackgroundColor = Color.Blue;
            //this.picDevice.skBackgroundImage = this.picDevice.Image;
            //this.picDevice.Visible = false;

            //Console.WriteLine(this.picDevice.mImageView.Frame.ToString());

#if DEBUG
            //RsController.Instance.ShareCode = "***********";
            //RsController.Instance.Password = "abcdefghijkl";
#endif


            //this.btnPwdStatus.skToolTip = this.Language.GetString("rs_show_password");
            //this.btnPwdStatus.skIcon = MyResource.GetImage("btn_main_hide_4.png");
            //this.txtLinkPwd.PasswordChar = '*';

            //this.txtLinkPwd.SelectionStart = 0;
            //this.txtLinkPwd.ScrollToCaret();
            //this.txtLinkPwd.Visible = true;

            //this.txtLinkPwd.ReadOnly = true;
            this.lblLinkPwd.Visible = false;
        }

        /// <summary>初始化控件位置</summary>
        /// 要在赋值完文本之后再初始化位置，不然按照控件高度计算位置会不准确
        private void InitLocation()
        {
            this.btnLinkState.Location = new Point(20, 0);
            this.lblInTestMode.Location = new Point(this.pnlNoCompany.Left, this.pnlNoCompany.Bottom + 1);

            this.picDevice.Location = new Point(0, this.pnlNoCompany.Height - this.picDevice.Height);
            this.lblNoManTitle.Location = new Point(0, this.picDevice.Top - 24 - this.lblNoManTitle.Height);
            this.lblNoManDetail.Location = new Point(0, this.lblNoManTitle.Top - 2 - this.lblNoManDetail.Height);
            this.btnBinding.Location = new Point(0, this.lblNoManDetail.Top - 24 - this.btnBinding.Height);
            this.btnPermissionsCount.Location = new Point(0, this.btnBinding.Top - 10 - this.btnPermissionsCount.Height);

            this.picCompany.Location = new Point(0, this.pnlCompany.Height - this.picCompany.Height);
            this.pnlCompanyName.Location = new Point(0, this.picCompany.Top - 10 - this.pnlCompanyName.Height);
            this.pnlCompanyName.skShowHorizontalBar = false;

            this.lblDeviceName.Location = new Point(0, this.pnlCompanyName.Height - this.lblDeviceName.Height);
            this.lblDeviceName.Margin = new Padding(0, 5, 0, 5);
            this.lblCompanyName.Location = new Point(0, this.lblDeviceName.Top - this.lblCompanyName.Height);
            this.lblCompanyName.Margin = new Padding(0, 5, 0, 10);

            this.pnlRSType.Location = new Point(0, this.lblCompanyName.Top - 5 - this.pnlRSType.Height);
            this.lblDeployTypeKey.Location = new Point(20, this.pnlRSType.Height - 11 - this.lblDeployTypeKey.Height);
            this.lblDeployTypeValue.Location = new Point(this.lblDeployTypeKey.Left, this.lblDeployTypeKey.Top - 3 - this.lblDeployTypeValue.Height);
            this.btnSwitchMode.Location = new Point(this.pnlRSType.Width - this.lblDeployTypeKey.Left - this.btnSwitchMode.Width, (this.pnlRSType.Height - this.btnSwitchMode.Height) / 2);

            this.pnlDeployTypeToolTip.Location = new Point(0, this.pnlCompanyName.Bottom - (this.pnlCompany.Height - this.pnlRSType.Top - 3 - this.pnlDeployTypeToolTip.Height));
            this.lblDeployTypeToolTip.Location = new Point(0, 0);

            this.pnlLinkCode.Location = new Point((this.Size.Width - this.pnlLinkCode.Width) / 2, this.btnLinkState.Frame.Bottom);
            this.lblLinkTitle.Location = new Point(30, this.pnlLinkCode.Height - 12 - this.lblLinkTitle.Height);
            this.lblLinkCodeTitle.Location = new Point(this.lblLinkTitle.Left, this.lblLinkTitle.Top - 8 - this.lblLinkCodeTitle.Height);
            this.lblLinkCode.Location = new Point(this.lblLinkTitle.Left + 3, this.lblLinkCodeTitle.Top - 5 - this.lblLinkCode.Height);
            this.pnlErrorCode.Location = new Point(this.lblLinkCode.Left + 3, this.lblLinkCode.Top + (this.lblLinkCode.Height - this.pnlErrorCode.Height) / 2);
            this.btnCopy.Location = new Point(this.lblLinkCode.Right + 5, this.lblLinkCode.Top + (this.lblLinkCode.Height - this.btnCopy.Height) / 2);
            this.pnlSplit.Location = new Point(this.lblLinkTitle.Left - this.pnlSplit.Width - 5, this.lblLinkTitle.Top + (this.lblLinkTitle.Height - this.pnlSplit.Height) / 2 + 1);

            this.lblLinkPwdTitle.Location = new Point(this.pnlLinkCode.Width / 2 + 5, this.lblLinkCodeTitle.Top + 4);
            this.txtLinkPwd.Location = new Point(this.lblLinkPwdTitle.Left, this.lblLinkCode.Top + (this.lblLinkCode.Height - this.txtLinkPwd.Height) / 2 - 2);
            this.lblLinkPwd.Location = this.txtLinkPwd.Location;
            this.pnlErrorPwd.Location = new Point(this.txtLinkPwd.Left + 3, this.txtLinkPwd.Top + (this.txtLinkPwd.Height - this.pnlErrorPwd.Height) / 2);
            this.btnPwdStatus.Location = new Point(this.txtLinkPwd.Right + 15, this.txtLinkPwd.Top + (this.txtLinkPwd.Height - this.btnPwdStatus.Height) / 2);
            this.btnUpdatePwd.Location = new Point(this.btnPwdStatus.Right + 3, this.txtLinkPwd.Top + (this.txtLinkPwd.Height - this.btnUpdatePwd.Height) / 2);
            this.lblLinkPwd.skToolTip = string.Empty;

            this.pnlLinkSplite.Location = new Point((this.pnlLinkCode.Width - this.pnlLinkSplite.Width) / 2 - 15, this.lblLinkPwd.Top + this.lblLinkPwd.Height / 2);
            this.btnLinkState.skIconPlaceText = 5;
            this.btnLinkState.skIconOffset = new Point(0, 0.5);

        }

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                MyAPI.LoadAccesssPower();

                this.FormBorderStyle = FormBorderStyle.None;
                this.skBorderRadius = new skBorderRadius(6);
                this.skBorderStrokeColor = Color.White;
                this.skBorderWidth = 0;

                this.skIcon = MyResource.GetImage("airdroid_48.png");
                this.ShowIcon = true;
                this.skIconSize = Size.Empty;
                this.mIconPadding = new Padding(20, -2, 0, 0);
                this.skBackgroundColor = Color.White;


                this.lblInTestMode.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;
                this.pnlCompany.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;
                this.pnlNoCompany.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;
                this.pnlLinkCode.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;
                this.btnLinkState.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;
                //this.pnlDeployTypeToolTip.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;

                this.mNormalImage = MyResource.GetImage("rs_logo_black.png");
                this.mAlternateImage = MyResource.GetImage("rs_logo_white.png");

                this.SetNotifyIconImage();

                this.mNotifyTimer = new System.Timers.Timer();
                this.mNotifyTimer.Interval = 600;
                this.mNotifyTimer.Elapsed += mNotifyTimer_Elapsed;

                this.tsmiExit.Title = this.Language.GetString("Login.Button.Exit");
                this.tsmFeedback.Title = this.Language.GetString("Business_Feedback_logs_title");
                this.tsmiCheckUpdate.Title = this.Language.GetString("MainMenu_Update");
                this.tsmiRefreshPwd.Text = this.Language.GetString("rs_refresh password");
                this.tsmiUpdataPwd.Text = this.Language.GetString("rs_custom_connection_password");
                this.tsmiUninstallApp.Title = this.Language.GetString("Common_Uninstall");

                //frmVNC.ShowMainLog(MyResource.GetImage("airdroid);

                this.pnlCompany.VisibleChanged += pnlCompany_VisibleChanged;
                this.pnlCompany.Visible = false;

                this.lblNoManTitle.skText = this.Language.GetString("Remote_BIZ_Enroll");
                this.lblNoManDetail.skText = this.Language.GetString("rs_organization_for_security_policy");
                //this.lblNoManDetail.AutoLayout();
                this.btnBinding.skText = this.Language.GetString("rs_enroll");

                this.lblDeviceName.skText = string.Format(this.Language.GetString("MainForm.Company.DeviceName"), MachineInfo.CurrentInfo.ComputerName);
                this.lblDeployTypeKey.skText = this.Language.GetString("rs_deploy_method");

                this.lblLinkTitle.skText = this.Language.GetString("rs_use_id_connect");
                this.lblLinkCodeTitle.skText = this.Language.GetString("rs_your_id");
                this.lblLinkPwdTitle.skText = this.Language.GetString("rs_one_time_password");
                this.btnLinkState.skText = this.Language.GetString("rs_onnection_not_ready");

                this.btnUpdatePwd.skToolTip = this.Language.GetString("rs_change_connection_password");
                this.btnCopy.skToolTip = this.Language.GetString("rs_copy_invitation_information");
                this.btnPwdStatus.skToolTip = this.Language.GetString("rs_show_password");

                this.btnPermissionsCount.skText = this.Language.GetString("rs_mac_system_permission_warning"); //系统权限未完全配置，部分远控功能可能受限

                //this.txtLinkPwd.skShowCaretNew = false;
                this.txtLinkPwd.Multiline = false;

                //this.cmsUpdatePwdMode.skBorderColor = skColor.FromArgb(211, 211, 211);
                this.tsmiUpdatePwdByDay.Text = this.Language.GetString("rs_today_password"); // 今日密码
                this.tsmiUpdatePwdByMan.Text = this.Language.GetString("rs_fixed_password"); // 固定密码
                this.tsmiUpdatePwdByConnection.Text = this.Language.GetString("rs_one_time_password"); // 单次密码

                this.CheckDeviceDeployInfo();
                this.CheckPwdTitle();

                string strModify = string.Empty;
                if (MyLog.IsTestMode)
                    strModify = string.Format(" ({0})", new FileInfo(Application.ExecutablePath).LastWriteTime.ToString("yyyy-MM-dd HH:mm"));

                this.lblVersion.skText = string.Format("v{0}{1}", Common.GetSoftVersion(), strModify);

                this.tmrStart.Start();

                this.InitLocation(); //有些位置要依靠控件高度去计算位置，所以在文本赋值之后再初始化控件位置

                //this.btnBinding.Padding = new Padding(10, 0, 10, 0);
                //this.btnBinding.skBadgeNumber = 3;
                //this.btnBinding.skShowIconMore = true;
                //this.btnBinding.skIconMoreAlign = skImageAlignment.Right;
                //this.btnBinding.skIconMore = MyResource.GetImage("ic_arrow.png");
                //this.btnBinding.skBadgeBackgroundColor = NSColor.Red;
                //this.btnBinding.skBadgeColor = NSColor.White;
                //this.btnBinding.skText = "通过生成 API 密钥，你可以为该密钥配置、认证和使用一个或多个 Apple 服务。密钥不会过期，但你无法在密钥生成后对其进行修改来访问更多服务。";
                //this.btnBinding.Size = new CGSize(this.btnBinding.Width, this.btnBinding.Height + 30);
                //this.btnBinding.skMultiLine = true;

                MyTest.Callback += OnTestCallback;
                MyTest.InitTestMenu(this.cmsBottom);

                // 监听菜单栏的Preferences菜单项
                this.SetupMenuHandlers();
            }
            catch (Exception ex)
            {
                LogStack(ex.StackTrace);
                Common.LogException(ex, "MainForm.InitControls");
            }
        }

        /// <summary>
        /// 设置菜单栏事件处理
        /// </summary>
        private void SetupMenuHandlers()
        {
            try
            {
                // 获取应用程序的主菜单
                NSApplication app = NSApplication.SharedApplication;
                if (app?.MainMenu != null)
                {
                    // 查找Preferences菜单项
                    NSMenuItem preferencesItem = FindMenuItemByIdentifier(app.MainMenu, "BOF-NM-1cW");
                    if (preferencesItem != null)
                    {
                        // 设置菜单项的Target和Action
                        preferencesItem.Target = this;
                        preferencesItem.Action = new ObjCRuntime.Selector("handlePreferencesMenu:");
                    }

                    // 查找Hide AirDroid Remote Support菜单项
                    NSMenuItem hideAppItem = FindMenuItemByTitle(app.MainMenu, "Hide AirDroid Remote Support");
                    if (hideAppItem != null)
                    {
                        // 设置菜单项的Target和Action
                        hideAppItem.Target = this;
                        hideAppItem.Action = new ObjCRuntime.Selector("handleHideApplication:");
                    }

                    // 设置多语言菜单项
                    this.SetupLocalizedMenuItems(app.MainMenu);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("SetupMenuHandlers error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 设置多语言菜单项
        /// </summary>
        private void SetupLocalizedMenuItems(NSMenu mainMenu)
        {
            try
            {
                // 设置应用菜单（Remote Support菜单）
                this.SetupAppMenuLocalization(mainMenu);
                
                // 设置Window菜单
                this.SetupWindowMenuLocalization(mainMenu);
            }
            catch (Exception ex)
            {
                Console.WriteLine("SetupLocalizedMenuItems error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 设置应用菜单的多语言
        /// </summary>
        private void SetupAppMenuLocalization(NSMenu mainMenu)
        {
            try
            {
                // 查找Remote Support菜单（应用菜单）
                NSMenuItem appMenuItem = FindMenuItemByTitle(mainMenu, "Remote Support");
                if (appMenuItem?.Submenu != null)
                {
                    //// 设置应用菜单标题
                    //appMenuItem.Title = this.Language.GetString("Menu_App_Title");
                    //Console.WriteLine($"SetupAppMenuLocalization: 设置应用菜单标题为: {appMenuItem.Title}");

                    // 设置About菜单项
                    NSMenuItem aboutItem = FindMenuItemByTitle(appMenuItem.Submenu, "About Remote Support");
                    if (aboutItem != null)
                    {
                        aboutItem.Title = this.Language.GetString("airdroid_remote_support_about");
                        Console.WriteLine($"SetupAppMenuLocalization: 设置About菜单项标题为: {aboutItem.Title}");
                    }

                    // 设置Preferences菜单项
                    NSMenuItem preferencesItem = FindMenuItemByTitle(appMenuItem.Submenu, "Preferences…");
                    if (preferencesItem != null)
                    {
                        preferencesItem.Title = this.Language.GetString("airdroid_remote_support_preferences");
                        Console.WriteLine($"SetupAppMenuLocalization: 设置Preferences菜单项标题为: {preferencesItem.Title}");
                    }

                    // 设置Hide应用菜单项
                    NSMenuItem hideAppItem = FindMenuItemByTitle(appMenuItem.Submenu, "Hide AirDroid Remote Support");
                    if (hideAppItem != null)
                    {
                        hideAppItem.Title = this.Language.GetString("airdroid_remote_support_hide");
                        Console.WriteLine($"SetupAppMenuLocalization: 设置Hide应用菜单项标题为: {hideAppItem.Title}");
                    }

                    // 设置Hide Others菜单项
                    NSMenuItem hideOthersItem = FindMenuItemByTitle(appMenuItem.Submenu, "Hide Others");
                    if (hideOthersItem != null)
                    {
                        hideOthersItem.Title = this.Language.GetString("airdroid_remote_support_hide_others");
                        Console.WriteLine($"SetupAppMenuLocalization: 设置Hide Others菜单项标题为: {hideOthersItem.Title}");
                    }

                    // 设置Show All菜单项
                    NSMenuItem showAllItem = FindMenuItemByTitle(appMenuItem.Submenu, "Show All");
                    if (showAllItem != null)
                    {
                        showAllItem.Title = this.Language.GetString("airdroid_remote_support_show_all");
                        Console.WriteLine($"SetupAppMenuLocalization: 设置Show All菜单项标题为: {showAllItem.Title}");
                    }

                    // 设置Quit菜单项
                    NSMenuItem quitItem = FindMenuItemByTitle(appMenuItem.Submenu, "Quit AirDroid Remote Support");
                    if (quitItem != null)
                    {
                        quitItem.Title = this.Language.GetString("airdroid_remote_support_quit");
                        Console.WriteLine($"SetupAppMenuLocalization: 设置Quit菜单项标题为: {quitItem.Title}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("SetupAppMenuLocalization error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 设置Window菜单的多语言
        /// </summary>
        private void SetupWindowMenuLocalization(NSMenu mainMenu)
        {
            try
            {
                // 查找Window菜单
                NSMenuItem windowMenuItem = FindMenuItemByTitle(mainMenu, "Window");
                if (windowMenuItem?.Submenu != null)
                {
                    // 设置自定义菜单项的多语言和Target
                    this.SetupCustomWindowMenuItems(windowMenuItem.Submenu);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("SetupWindowMenuLocalization error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 设置自定义Window菜单项的多语言和Target
        /// </summary>
        private void SetupCustomWindowMenuItems(NSMenu windowSubmenu)
        {
            try
            {
                // 设置Minimize菜单项
                NSMenuItem minimizeItem = FindMenuItemByTitle(windowSubmenu, "Minimize");
                if (minimizeItem != null)
                {
                    minimizeItem.Title = this.Language.GetString("airdroid_remote_support_minimize");
                    minimizeItem.Target = this;
                    minimizeItem.Action = new ObjCRuntime.Selector("handleMinimizeWindow:");
                    Console.WriteLine($"SetupCustomWindowMenuItems: 设置Minimize菜单项标题为: {minimizeItem.Title}");
                }

                // 设置前置所有窗口菜单项
                NSMenuItem bringAllToFrontItem = FindMenuItemByTitle(windowSubmenu, "Bring All to Front");
                if (bringAllToFrontItem != null)
                {
                    bringAllToFrontItem.Title = this.Language.GetString("window_bring_all_to_front");
                    Console.WriteLine($"SetupCustomWindowMenuItems: 设置Bring All to Front菜单项标题为: {bringAllToFrontItem.Title}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("SetupCustomWindowMenuItems error: " + ex.ToString());
            }
        }



        /// <summary>
        /// 获取需要移除的系统菜单项关键词
        /// </summary>
        private List<string[]> GetMenuItemsToRemove()
        {
            // 返回需要移除的菜单项关键词列表
            // 只列出明确不需要的菜单项
            return new List<string[]>
            {
                // 移除Zoom/填充功能
                new[] { "zoom", "填充", "缩放", "ズーム", "확대/축소" },
                
                // 移除Center/居中功能
                new[] { "center", "居中", "センタリング", "가운데" },
                
                // 移除Tab相关功能
                new[] { "move tab to new window", "将标签页移到新窗口", "タブを新しいウインドウに移動" },
                
                // 移除Merge功能
                new[] { "merge all windows", "合并所有窗口", "すべてのウインドウを結合" }
                
                // 注意：不移除全屏和拼贴功能，让它们保持显示
            };
        }

        /// <summary>
        /// 判断是否是系统菜单项
        /// </summary>
        private bool IsSystemMenuItem(string title)
        {
            if (string.IsNullOrEmpty(title)) return false;

            // 系统菜单项的关键词列表
            var systemMenuKeywords = new[]
            {
                "zoom", "缩放", "ズーム", "확대/축소",
                "full screen", "全屏", "フルスクリーン", "전체 화면",
                "center", "居中", "センタリング", "가운데",
                "tile", "拼贴", "タイル", "타일",
                "tab", "标签", "タブ", "탭",
                "merge", "合并", "マージ", "병합",
                "replace", "替换", "置換", "교체",
                "remove", "移除", "削除", "제거"
            };

            string lowerTitle = title.ToLower();
            return systemMenuKeywords.Any(keyword => lowerTitle.Contains(keyword.ToLower()));
        }

        /// <summary>
        /// 清理不需要的系统Window菜单项
        /// </summary>
        private void CleanupSystemWindowMenuItems(NSMenu windowSubmenu)
        {
            try
            {
                var menuItemsToRemove = this.GetMenuItemsToRemove();
                var itemsToRemove = new List<NSMenuItem>();

                Console.WriteLine($"CleanupSystemWindowMenuItems: 开始清理，当前菜单项数量: {windowSubmenu.Items.Length}");

                // 遍历所有菜单项
                foreach (NSMenuItem item in windowSubmenu.Items)
                {
                    if (item.IsSeparatorItem) continue;

                    string title = item.Title;
                    Console.WriteLine($"CleanupSystemWindowMenuItems: 检查菜单项: {title}");

                    // 检查是否匹配需要移除的菜单项
                    bool shouldRemove = false;
                    
                    foreach (var keywords in menuItemsToRemove)
                    {
                        if (this.IsMenuItemType(title, keywords))
                        {
                            shouldRemove = true;
                            Console.WriteLine($"CleanupSystemWindowMenuItems: 标记移除菜单项: {title} (匹配关键词: {string.Join(", ", keywords)})");
                            break;
                        }
                    }

                    if (shouldRemove)
                    {
                        itemsToRemove.Add(item);
                    }
                }

                // 移除标记的菜单项
                foreach (var item in itemsToRemove)
                {
                    try
                    {
                        windowSubmenu.RemoveItem(item);
                        Console.WriteLine($"CleanupSystemWindowMenuItems: 成功移除菜单项: {item.Title}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"CleanupSystemWindowMenuItems: 移除菜单项失败 {item.Title}: {ex.Message}");
                    }
                }

                Console.WriteLine($"CleanupSystemWindowMenuItems: 清理完成，移除了 {itemsToRemove.Count} 个菜单项");
            }
            catch (Exception ex)
            {
                Console.WriteLine("CleanupSystemWindowMenuItems error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 判断菜单项是否匹配指定的类型
        /// </summary>
        private bool IsMenuItemType(string title, string[] keywords)
        {
            if (string.IsNullOrEmpty(title)) return false;
            
            string lowerTitle = title.ToLower();
            return keywords.Any(keyword => lowerTitle.Contains(keyword.ToLower()));
        }

        /// <summary>
        /// 启动菜单清理定时器
        /// </summary>
        private void StartMenuCleanupTimer()
        {
            try
            {
                // 创建定时器，每2秒检查一次菜单
                TimerMgr.Create(2, () => {
                    try
                    {
                        this.BeginInvoke(new Action(() => {
                            var app = NSApplication.SharedApplication;
                            if (app?.MainMenu != null)
                            {
                                NSMenuItem windowMenuItem = FindMenuItemByTitle(app.MainMenu, "Window");
                                if (windowMenuItem?.Submenu != null)
                                {
                                    this.CleanupSystemWindowMenuItems(windowMenuItem.Submenu);
                                }
                            }
                        }));
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("MenuCleanupTimer error: " + ex.ToString());
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine("StartMenuCleanupTimer error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 递归查找指定ID的菜单项
        /// </summary>
        private NSMenuItem FindMenuItemByIdentifier(NSMenu menu, string identifier)
        {
            if (menu?.Items == null) return null;

            foreach (NSMenuItem item in menu.Items)
            {
                // 检查当前菜单项的标识符（这里我们通过title来识别）
                if (item.Title == "Preferences…")
                {
                    return item;
                }

                // 如果有子菜单，递归查找
                if (item.Submenu != null)
                {
                    NSMenuItem found = FindMenuItemByIdentifier(item.Submenu, identifier);
                    if (found != null) return found;
                }
            }

            return null;
        }

        /// <summary>
        /// 递归查找指定标题的菜单项
        /// </summary>
        private NSMenuItem FindMenuItemByTitle(NSMenu menu, string title)
        {
            if (menu?.Items == null) return null;

            foreach (NSMenuItem item in menu.Items)
            {
                // 检查当前菜单项的标题
                if (item.Title == title)
                {
                    return item;
                }

                // 如果有子菜单，递归查找
                if (item.Submenu != null)
                {
                    NSMenuItem found = FindMenuItemByTitle(item.Submenu, title);
                    if (found != null) return found;
                }
            }

            return null;
        }

        /// <summary>
        /// 处理Preferences菜单点击事件
        /// </summary>
        [Export("handlePreferencesMenu:")]
        public void HandlePreferencesMenu(NSObject sender)
        {
            try
            {
                // 调用设置按钮的点击事件
                this.btnSetting_Click(sender, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                Console.WriteLine("HandlePreferencesMenu error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 处理Hide AirDroid Remote Support菜单点击事件
        /// </summary>
        [Export("handleHideApplication:")]
        public void HandleHideApplication(NSObject sender)
        {
            try
            {
                // 先调用CloseOrMinTip检查是否需要显示睡眠提示
                this.CloseOrMinTip(new Action(() => {
                    // 执行隐藏应用的操作
                    NSApplication.SharedApplication.Hide(sender);
                }));
            }
            catch (Exception ex)
            {
                Console.WriteLine("HandleHideApplication error: " + ex.ToString());
            }
        }

        /// <summary>
        /// 处理Minimize菜单点击事件
        /// </summary>
        [Export("handleMinimizeWindow:")]
        public void HandleMinimizeWindow(NSObject sender)
        {
            try
            {
                Console.WriteLine("HandleMinimizeWindow: 开始处理最小化菜单点击");
                
                // 先调用CloseOrMinTip检查是否需要显示睡眠提示
                this.CloseOrMinTip(new Action(() => {
                    Console.WriteLine("HandleMinimizeWindow: 执行最小化操作");
                    
                    try
                    {
                        // 使用NSWindow的原生最小化方法
                        if (this.Window != null)
                        {
                            this.Window.Miniaturize(sender);
                            Console.WriteLine("HandleMinimizeWindow: 使用NSWindow.Miniaturize完成最小化");
                        }
                    }
                    catch (Exception ex1)
                    {
                        Console.WriteLine("HandleMinimizeWindow: NSWindow.Miniaturize失败: " + ex1.Message);
                        
                        try
                        {
                            // 回退到WindowState设置
                            base.WindowState = FormWindowState.Minimized;
                            Console.WriteLine("HandleMinimizeWindow: 使用WindowState完成最小化");
                        }
                        catch (Exception ex2)
                        {
                            Console.WriteLine("HandleMinimizeWindow: WindowState设置失败: " + ex2.Message);
                        }
                    }
                }));
            }
            catch (Exception ex)
            {
                Console.WriteLine("HandleMinimizeWindow error: " + ex.ToString());
            }
        }

        public void OnTestCallback(object sender, TestArg e)
        {
            this.lblInTestMode.Top = this.pnlCompany.Bottom + 1;
            this.lblInTestMode.Visible = e.Visible;
            this.lblInTestMode.skText = e.Title;

            if (e.Type == MenuType.MenuClosed)
                this.OnMenuClosed(sender, e);

            if (e.NeedLoadUrl)
            {
                RsAPI.LoadDeployInfo();
                CheckDeviceDeployInfo();
            }

            SocketHandler socket = SocketMgr.GetSocket(SocketType.UserDisplay);
            if (socket != null)
                socket.SendTestInfo(MyLog.IsTestMode, MyLog.ShowLog);
        }

        public override void OnWindowBecomeKey(object sender, EventArgs e)
        {
            base.OnWindowBecomeKey(sender, e);

            MyAPI.LoadAccesssPower();
        }

        public void pnlCompany_VisibleChanged(object sender, EventArgs e)
        {
            LogStack("pnlCompany_VisibleChanged -> Visible = " + this.pnlCompany.Visible.ToString());
        }

        private void lblLinkState_Click(object sender, EventArgs e)
        {

        }

        private void btnBinding_Click(object sender, EventArgs e)
        {
            this.mActionHelper.Add(tdActionModeKeyForRS.MainJoinEnterprise);

            if (this.frmBinding != null)
            {
                this.frmBinding.Activate();
                return;
            }

            if (this.frmBinding == null)
            {
                this.frmBinding = new rsBinding();
                this.frmBinding.FormClosing += frmBinding_FormClosed;
                this.frmBinding.Show(this);
            }
        }

        public void frmBinding_FormClosed(object sender, FormClosingEventArgs e)
        {
            this.ShowPanelCompany(RsAPI.DeviceDeployInfo);
            this.frmBinding = null;
        }

        private delegate void ShowPanelCompanyHandler(rsDeviceDeployInfo info, bool logStack);
        public void ShowPanelCompany(rsDeviceDeployInfo info, bool logStack = true)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    LogStack(string.Format("ShowPanelCompany.BeginInvoke -> info = {0}", RsAPI.DeviceDeployInfo == null ? "null" : MyJson.SerializeToJsonString(RsAPI.DeviceDeployInfo)));

                    this.BeginInvoke(new ShowPanelCompanyHandler(this.ShowPanelCompany), info, false);
                }
                else
                {
                    if (logStack)
                        LogStack(string.Format("ShowPanelCompany -> info = {0}", RsAPI.DeviceDeployInfo == null ? "null" : MyJson.SerializeToJsonString(RsAPI.DeviceDeployInfo)));


                    if (info == null)
                    {
                        this.pnlCompany.Visible = false;
                        this.pnlNoCompany.Visible = true;
                    }
                    else
                    {
                        this.pnlCompany.Visible = true;
                        this.pnlNoCompany.Visible = false;

                        this.lblDeviceName.skText = string.Format(this.Language.GetString("rs_pc_added_to"), info.device_name);

                        this.lblCompanyName.skText = (string.IsNullOrEmpty(info.organization_name) ? info.company_name : info.organization_name) + "";


                        if (info.device_type == skDeviceType.RS_No_Man_Mac)
                        {
                            this.picCompany.skBackgroundImage = MyResource.GetImage("pic_main_unattended_bg.png");
                            this.lblDeployTypeValue.skText = this.Language.GetString("rs_unattached_mode");
                            this.btnSwitchMode.skText = this.Language.GetString("rs_attached_mode");
                            this.btnSwitchMode.Location = new Point(pnlRSType.Width - 16 - this.btnSwitchMode.Size.Width, this.btnSwitchMode.Location.Y);
                            this.lblDeployTypeToolTip.skText = string.Format(this.Language.GetString("rs_pc_unattended_control_1"), this.lblCompanyName.skText);
                        }
                        else
                        {
                            this.picCompany.skBackgroundImage = MyResource.GetImage("pic_main_attended_bg.png");
                            this.lblDeployTypeValue.skText = this.Language.GetString("rs_attached_mode");
                            this.btnSwitchMode.skText = this.Language.GetString("rs_unattached_mode");
                            this.btnSwitchMode.Location = new Point(pnlRSType.Width - 16 - this.btnSwitchMode.Size.Width, this.btnSwitchMode.Location.Y);
                            this.lblDeployTypeToolTip.skText = string.Format(this.Language.GetString("rs_managed_someone_consent"), this.lblCompanyName.skText);
                        }

                        this.lblDeployTypeToolTip.Size = new Size(this.pnlRSType.Size.Width, this.lblDeployTypeToolTip.Size.Height);
                        this.pnlDeployTypeToolTip.Size = this.lblDeployTypeToolTip.Size;

                        this.lblDeployTypeToolTip.Location = new Point(0, 0);
                    }

                    this.SetPessionsTipState();
                }
            }
            catch (Exception ex)
            {
                LogStack("ShowPanelCompany\r\n" + ex.StackTrace);
            }
        }

        private void btnDeployTypeTip_MouseEnter(object sender, EventArgs e)
        {
            this.mActionHelper.Add(RsAPI.DeviceDeployInfo != null && RsAPI.DeviceDeployInfo.device_type == skDeviceType.RS_No_Man_Mac ? tdActionModeKeyForRS.SummaryNomanClickTip : tdActionModeKeyForRS.SummaryManClickTip);

            //this.pnlDeployTypeToolTip.Location = new Point(0, this.pnlCompanyName.Bottom - (this.pnlCompany.Height - (this.pnlRSType.Top - 5) - this.pnlDeployTypeToolTip.Height));
            this.pnlDeployTypeToolTip.Location = new Point(0, this.pnlRSType.Bottom - this.lblDeployTypeKey.Height - this.lblDeployTypeValue.Height - this.pnlDeployTypeToolTip.Height);
            this.pnlDeployTypeToolTip.Visible = true;
            this.pnlDeployTypeToolTip.BringToFront();

            //this.pnlDeployTypeToolTip.skBackgroundColor = Color.Black;
            //this.lblDeployTypeToolTip.skBackgroundColor = Color.Red;
            //this.lblDeployTypeToolTip.skBackgroundColor = skColor.FromArgb(76, 76, 76);

            //this.pnlDeployTypeToolTip.Location = new Point(0, this.pnlRSType.Top - this.pnlDeployTypeToolTip.Height - 5);

            //this.pnlCompanyName.AutoLayout();

            //Console.WriteLine("pnlDeployTypeToolTip.Frame = " + this.pnlDeployTypeToolTip.Frame.ToString());
            //Console.WriteLine("lblDeployTypeToolTip.Frame = " + this.lblDeployTypeToolTip.Frame.ToString());
        }

        private void btnDeployTypeTip_MouseLeave(object sender, EventArgs e)
        {
            this.pnlDeployTypeToolTip.Visible = false;
        }

        private void btnPermissionSetting_Click(object sender, EventArgs e)
        {
            //this.mActionHelper.Add(tdActionModeKeyForRS.MainJoinEnterprise);

            this.ShowPerssionsWindow();
        }

        private void btnSwitchMode_Click(object sender, EventArgs e)
        {
            if (!Common.NetworkIsAvailable())
            {
                RsCommon.ShowSplashBox(this, this.Language.GetString("Common_check_network"));
                return;
            }

            this.mActionHelper.Add(RsAPI.DeviceDeployInfo != null && RsAPI.IsNoMan() ? tdActionModeKeyForRS.SummaryNomanClickSwicth : tdActionModeKeyForRS.SummaryManClickSwicth);

            rsDeviceDeployInfo deploy = RsAPI.DeviceDeployInfo;
            if (deploy.device_type == skDeviceType.RS_Man_Mac && deploy.has_unattended_func != 1)
            {
                //skSplashBox.Show("##请升级订单，您当前没有切换到无人值守的权限", this);
                return;
            }

            string msgTitle = string.Empty;
            string msgContent = string.Empty;

            bool showInputPwd = RsAPI.IsNoMan() && (RsAPI.DeviceAuthInfo.close_unattended_daemon_by_pwd == rsDeviceActionStatus.Open || !SocketMgr.IsConnected);
            if (deploy.device_type == skDeviceType.RS_No_Man_Mac)
            {
                msgTitle = string.Format(this.Language.GetString("rs_pc_switch_to"), this.Language.GetString("rs_attached_mode"));

                if (showInputPwd)
                    msgContent = this.Language.GetString("rs_switch_to_attend_tip");
                else
                    msgContent = this.Language.GetString("rs_unattached_switch_dialog_message");
            }
            else
            {
                msgTitle = string.Format(this.Language.GetString("rs_pc_switch_to"), this.Language.GetString("rs_unattached_mode"));
                msgContent = string.Format(this.Language.GetString("rs_pc_sure_switch_to_unattended"), string.IsNullOrEmpty(deploy?.organization_name) ? deploy.company_name : deploy.organization_name);
            }

            skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(msgContent, msgTitle, this.Language);
            skMsgInfo.ButtonPadding = new Padding(10, 2, 10, 0);
            if (deploy.device_type == skDeviceType.RS_Man_Mac)
            {
                skMsgInfo.FirstButton.EnableTime = 5;
            }

            if (showInputPwd)
            {
                skMsgInfo.InputBoxInfo.Visible = true;
                skMsgInfo.InputBoxInfo.Padding = new Padding(10, 8, 5, 24);
                skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 6);

                skMsgInfo = RsCommon.SetClearMsgInfo(skMsgInfo, MyResource.GetImage("btn_clear_4.png"));

                skMsgInfo = RsCommon.SetPasswordMsgInfo(skMsgInfo, MyResource.GetImage("btn_main_hide_4.png"), MyResource.GetImage("btn_main_show_4.png"));
            }

            skMsgInfo.EventCallBack += () => {
                this.mActionHelper.Add(RsAPI.IsNoMan() ? tdActionModeKeyForRS.SummaryNomanClickSwicthOK : tdActionModeKeyForRS.SummaryManClickSwicthOK);
                if (showInputPwd && string.IsNullOrEmpty(skMsgInfo.InputBoxInfo.skText))
                {
                    RsCommon.SetErrorMsgInfo(skMsgInfo, this.Language.GetString("Login.Label.PasswordEmpty"));
                    MsgBoxMgr.StopBtnLoading(skMsgInfo);
                    return;
                }

                ThreadMgr.StartWithPara(this.ThreadUpdateDeviceType, skMsgInfo);

                if (!RsAPI.IsNoMan() && this.IsNeedShowPessionsTip()) //切成无人值守，未获取所有权限，要弹窗
                {
                    this.ShowPerssionsWindow();
                }
            };

            MsgBoxMgr.Show(this, skMsgInfo);
        }

        public void ThreadUpdateDeviceType(object state)
        {
            skMsgInfoNew msg = state as skMsgInfoNew;
            msg.ButtonPadding = new Padding(10, 2, 10, 0);
            try
            {
                string pwd = msg.InputBoxInfo.skText;
                string errMsg = string.Empty;

                rsDeviceDeployInfo info = RsAPI.DeviceDeployInfo;
                RSEventType eventType = RsAPI.UpdateDeviceType(RsAPI.DeviceDeployInfo, pwd);

                if (eventType == RSEventType.DeviceTypeUpdateSuccessed)
                {
                    MsgBoxMgr.Remove(msg);
                    RsCommon.ShowSplashBox(this, this.Language.GetString("Parent_Device_Switch"));
                    this.ShowPanelCompany(RsAPI.DeviceDeployInfo);
                    return;
                }

                switch (eventType)
                {
                    case RSEventType.DeviceTypeUpdateErrorByPara:
                        RsCommon.ShowSplashBox(this, this.Language.GetString("Profile_NickName_ParameterError"));
                        break;

                    case RSEventType.DeviceTypeUpdateErrorByPwd:
                        RsCommon.SetErrorMsgInfo(msg, this.Language.GetString("rs_enter_password_error"));
                        break;

                    case RSEventType.DeviceTypeUpdateErrorByFreeDeviceNumberLimit:
                    case RSEventType.DeviceTypeUpdateErrorByVipLevelLimit:
                    case RSEventType.DeviceTypeUpdateErrorByVipDeviceNumberLimit:
                        this.Invoke(new Action(() => {
                            skMsgInfoNew msgGotIt = RsCommon.GetDefaultMsgInfo(this.Language.GetString("rs_bound_devices_limit_to_purchase"), this.Language.GetString("pc_rs_intro_title"), this.Language);
                            msgGotIt.ButtonPadding = new Padding(10, 2, 10, 0);
                            msgGotIt.FirstButton.Visible = false;
                            msgGotIt.SecondButton.skText = this.Language.GetString("Common_gotit_tip");
                            msgGotIt.SecondButton.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
                            msgGotIt.SecondButton.skTextColor = Color.White;
                            msgGotIt.SecondButton.skBackgroundImage = MyResource.GetImage("rs_btn_blue_4.png");
                            msgGotIt.TopMost = true;
                            msgGotIt.FormDialog = true;

                            MsgBoxMgr.Show(MsgBoxMgr.Get(msg), msgGotIt);

                            MsgBoxMgr.Remove(msg);
                        }));
                        break;
                    case RSEventType.DeviceTokenUpdate:
                        RsCommon.ShowSplashBox(this, this.Language.GetString("rs_pc_request_failed"));
                        RsCommon.DeviceTokenUpdate();
                        break;
                    default:
                        RsCommon.ShowSplashBox(this, this.Language.GetString("Common_check_network"));
                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ThreadUpdateDeviceType");
            }
            finally
            {
                MsgBoxMgr.StopBtnLoading(msg);
            }
        }

        private void SwitchBtnStateHandle(bool loading)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() => {
                    this.SwitchBtnStateHandle(loading);
                }));
            }
            else
            {
                if (loading)
                {
                    this.btnSwitchMode.skIconGif = MyResource.GetGif("loading_grey.png");
                    this.btnSwitchMode.skIconState = skImageState.OneState;
                    this.btnSwitchMode.LoadGif();
                }
                else
                {
                    this.btnSwitchMode.StopAnimate();
                    this.btnSwitchMode.skIconSize = new Size(16, 16);
                    this.btnSwitchMode.skIconState = skImageState.FourState;
                    this.btnSwitchMode.skIcon = MyResource.GetImage("btn_main_switch_4.png");
                }
            }
        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            // 检查当前用户是否为管理员
            if (!ServiceMgr.IsCurrentUserAdmin())
            {
                // 如果不是管理员，触发系统管理员权限弹窗
                this.ShowAdminPasswordDialog();
                return;
            }

            // 如果是管理员，正常打开设置窗口
            if (frmSetting == null)
            {
                frmSetting = new rsSetting();
                frmSetting.Show(this);
                frmSetting.Activate();
                frmSetting.FormClosing += frmSetting_FormClosing;
            }
            else
            {
                frmSetting.WindowState = FormWindowState.Normal;
                frmSetting.Show(this);
                frmSetting.Activate();
            }
        }

        private void frmSetting_FormClosing(object sender, FormClosingEventArgs e)
        {
            frmSetting.FormClosing -= frmSetting_FormClosing;
            frmSetting = null;

            this.SendSettingChanged();
        }

        private void frmPerssion_FormClosing(object sender, FormClosingEventArgs e)
        {
            frmPerssion.FormClosing -= frmPerssion_FormClosing;
            frmPerssion = null;
        }

        private void lblNoManTitle_DoubleClick(object sender, EventArgs e)
        {
            this.ShowPanelCompany(RsAPI.DeviceDeployInfo);
        }

        private void lblNoManDetail_Click(object sender, EventArgs e)
        {
            this.ShowPanelCompany(RsAPI.DeviceDeployInfo);
        }

        /// <summary>
        /// 打开连接密码菜单 - 使用原生NSPopUpButton替代方案
        /// </summary>
        private void lblLinkPwdTitle_Click(object sender, EventArgs e)
        {
            // 创建原生NSPopUpButton菜单
            this.ShowNativePasswordMenu();
        }

        /// <summary>
        /// 显示原生密码菜单
        /// </summary>
        private void ShowNativePasswordMenu()
        {
            try
            {
                // 创建原生NSMenu
                NSMenu nativeMenu = new NSMenu();

                // 添加菜单项
                var todayItem = new NSMenuItem(this.Language.GetString("rs_today_password"), new ObjCRuntime.Selector("selectTodayPassword:"), "");
                todayItem.Target = this;

                var fixedItem = new NSMenuItem(this.Language.GetString("rs_fixed_password"), new ObjCRuntime.Selector("selectFixedPassword:"), "");
                fixedItem.Target = this;

                var oneTimeItem = new NSMenuItem(this.Language.GetString("rs_one_time_password"), new ObjCRuntime.Selector("selectOneTimePassword:"), "");
                oneTimeItem.Target = this;

                // 设置当前选中项的标记
                int currentMode = (int)RsAPI.LocalSetting.PwdType;
                if (currentMode == (int)rsPwdType.UpdateByDay)
                    todayItem.State = NSCellStateValue.On;
                else if (currentMode == (int)rsPwdType.UpdateByMan)
                    fixedItem.State = NSCellStateValue.On;
                else
                    oneTimeItem.State = NSCellStateValue.On;

                nativeMenu.AddItem(todayItem);
                nativeMenu.AddItem(fixedItem);
                nativeMenu.AddItem(oneTimeItem);

                // 计算菜单显示位置
                CGPoint menuLocation = new CGPoint(
                    this.lblLinkPwdTitle.Frame.Left,
                    this.lblLinkPwdTitle.Frame.Bottom + 5
                );

                // 创建事件并显示菜单
                NSEvent evt = NSEvent.OtherEvent(NSEventType.ApplicationDefined,
                    menuLocation,
                    (NSEventModifierMask)0, 0,
                    this.Window.WindowNumber, this.Window.GraphicsContext,
                    (short)NSEventType.ApplicationDefined, 0, 0);

                NSMenu.PopUpContextMenu(nativeMenu, evt, this.lblLinkPwdTitle);
            }
            catch (Exception ex)
            {
                // 如果原生菜单失败，回退到原来的菜单
                this.UpdateMenuIcon();
                this.cmsUpdatePwdMode.Show(this.lblLinkPwdTitle, new Point(this.lblLinkPwdTitle.skIconMoreRect.Left - 3, this.lblLinkTitle.skIconMoreRect.Top - 2));
            }
        }

        [Export("selectTodayPassword:")]
        private void SelectTodayPassword(NSMenuItem sender)
        {
            this.UpdatePasswordType(rsPwdType.UpdateByDay);
        }

        [Export("selectFixedPassword:")]
        private void SelectFixedPassword(NSMenuItem sender)
        {
            this.UpdatePasswordType(rsPwdType.UpdateByMan);
        }

        [Export("selectOneTimePassword:")]
        private void SelectOneTimePassword(NSMenuItem sender)
        {
            this.UpdatePasswordType(rsPwdType.UpdateAfterDisconnect);
        }

        private void UpdatePasswordType(rsPwdType pwdType)
        {
            try
            {
                rsLocalSettingInfo setting = RsAPI.LocalSetting;
                setting.PwdType = pwdType;
                RsAPI.SaveLocalSetting(setting);

                this.CheckPwdTitle(); // 更新显示的密码类型文本
                this.SendSettingChanged();
            }
            catch (Exception ex)
            {
                // 忽略错误，继续执行
            }
        }

        /// <summary>
        /// 显示原生的更新密码菜单
        /// </summary>
        private void ShowNativeUpdatePwdMenu()
        {
            try
            {
                // 创建原生菜单
                NSMenu nativeMenu = new NSMenu();
                nativeMenu.AutoEnablesItems = false;

                // 添加刷新密码菜单项
                var refreshItem = new NSMenuItem(this.Language.GetString("rs_refresh password"), new ObjCRuntime.Selector("refreshPassword:"), "");
                refreshItem.Target = this;
                refreshItem.Image = this.LoadMenuIcon("btn_main_refresh_1.png");

                // 添加修改密码菜单项
                var updateItem = new NSMenuItem(this.Language.GetString("rs_custom_connection_password"), new ObjCRuntime.Selector("updatePassword:"), "");
                updateItem.Target = this;
                updateItem.Image = this.LoadMenuIcon("btn_update_1.png");

                nativeMenu.AddItem(refreshItem);
                nativeMenu.AddItem(updateItem);

                // 计算菜单显示位置
                CGPoint menuLocation = new CGPoint(
                    this.btnUpdatePwd.Frame.Left,
                    this.btnUpdatePwd.Frame.Bottom + 5
                );

                // 创建事件并显示菜单
                NSEvent evt = NSEvent.OtherEvent(NSEventType.ApplicationDefined,
                    menuLocation,
                    (NSEventModifierMask)0, 0,
                    this.Window.WindowNumber, this.Window.GraphicsContext,
                    (short)NSEventType.ApplicationDefined, 0, 0);

                NSMenu.PopUpContextMenu(nativeMenu, evt, this.btnUpdatePwd);
            }
            catch (Exception ex)
            {
                // 如果原生菜单失败，回退到原来的菜单
                this.cmsPwd.Show(btnUpdatePwd);
            }
        }

        [Export("refreshPassword:")]
        private void RefreshPassword(NSMenuItem sender)
        {
            this.tsmiRefreshPwd_Click(null, null);
        }

        [Export("updatePassword:")]
        private void UpdatePassword(NSMenuItem sender)
        {
            this.tsmiUpdataPwd_Click(null, null);
        }

        /// <summary>
        /// 加载菜单图标
        /// </summary>
        private NSImage LoadMenuIcon(string iconName)
        {
            try
            {
                // 使用MyResource.GetIcon方法，它专门为菜单图标设计，返回16x16大小的NSImage
                var nsImage = MyResource.GetIcon(iconName);
                return nsImage;
            }
            catch (Exception ex)
            {
                return null;
            }
        }



        private void UpdateMenuIcon()
        {
            int selectMode = (int)RsAPI.LocalSetting.PwdType;
            if (selectMode >= 0 && selectMode < this.cmsUpdatePwdMode.Items.Count)
            {
                int maxCount = this.cmsUpdatePwdMode.Items.Count;
                for (int i = 0; i < maxCount; i++)
                {
                    skMenuItem item = this.cmsUpdatePwdMode.Items[i];
                    item.Image = (i == selectMode ? MyResource.GetImage("cms_updatePwdMode_selected_icon.png") : null);
                }
            }
        }

        /// <summary>
        /// 打开连接密码菜单然后切换图标
        /// </summary>
        private void cmsUpdatePwdMode_Opening(object sender, EventArgs e)
        {
            // 释放之前的图片
            this.lblLinkPwdTitle.skIconMore = MyResource.GetImage("btn_arrow_up_icon.png");
        }

        /// <summary>
        /// 关闭连接密码菜单然后切换图标
        /// </summary>
        private void cmsUpdatePwdMode_Closing(object sender, EventArgs e)
        {
            // 释放之前的图片
            this.lblLinkPwdTitle.skIconMore = MyResource.GetImage("btn_arrow_down_icon.png");
        }

        /// <summary>
        /// 点击选择自定义密码模式
        /// </summary>
        private void cmsUpdatePwdMode_ItemClicked(object sender, EventArgs e)
        {
            ToolStripItem menuItem = sender as ToolStripItem;
            if (menuItem != null)
            {
                string choseStr = menuItem.skTag.ToString();
                rsLocalSettingInfo setting = RsAPI.LocalSetting;
                foreach (rsPwdType pwdType in Enum.GetValues(typeof(rsPwdType)))
                {
                    if (string.Equals(choseStr, pwdType.ToString()))
                    {
                        setting.PwdType = pwdType;
                        break;
                    }
                }
                RsAPI.SaveLocalSetting(setting);
                this.UpdateMenuIcon();

                this.SendSettingChanged();
            }
        }

        private void lblLinkPwd_DoubleClick(object sender, EventArgs e)
        {
            //this.txtLinkPwd.Visible = true;
            //this.lblLinkPwd.Visible = false;

            //this.lblLinkCode_DoubleClick(null, null);

            //this.txtLinkPwd.Focus();
            //this.txtLinkPwd.SelectAll(); // 全选

            if (!MyLog.IsTestMode)
                return;

            string strPwd = RsController.Instance.Password;
            if (string.IsNullOrEmpty(strPwd))
                return;

            Clipboard.SetText(strPwd);
            RsCommon.ShowSplashBox(this, this.Language.GetString("rs_copy_board_scuuess"));
        }

        /// <summary>
        /// 设置窗口中密码更新方式发生改变时，更新主窗体的密码标题显示
        /// </summary>
        private void OnSettingPwdUpdate()
        {
            this.CheckPwdTitle();
        }

        /// <summary>
        /// 显示系统管理员权限弹窗
        /// </summary>
        private void ShowAdminPasswordDialog()
        {
            try
            {
                // 使用AppleScript触发系统管理员权限弹窗
                string script = "do shell script \"echo 'Checking admin privileges'\" with prompt \"隐私与安全性 wants to modify your system settings.\" with administrator privileges";

                string result = Common.RunShell("/usr/bin/osascript", "-e", script);

                // 如果用户输入了正确的管理员密码，重新检查权限并打开设置
                if (ServiceMgr.IsCurrentUserAdmin())
                {
                    // 用户已获得管理员权限，打开设置窗口
                    if (frmSetting == null)
                    {
                        frmSetting = new rsSetting();
                        frmSetting.Show(this);
                        frmSetting.Activate();
                        frmSetting.FormClosing += frmSetting_FormClosing;
                    }
                    else
                    {
                        frmSetting.WindowState = FormWindowState.Normal;
                        frmSetting.Show(this);
                        frmSetting.Activate();
                    }
                }
                else
                {
                    return;

                    // 用户取消了权限输入或密码错误
                    //RsCommon.ShowSplashBox(this, this.Language.GetString("Common_system_permission_disabled"));
                }
            }
            catch (Exception ex)
            {
                // 如果AppleScript执行失败，显示权限不足的提示
                RsCommon.ShowSplashBox(this, this.Language.GetString("Common_system_permission_disabled"));
                Common.LogException(ex, "ShowAdminPasswordDialog");
            }
        }
    }
}
