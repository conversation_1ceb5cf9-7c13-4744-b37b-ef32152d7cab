<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="9531" systemVersion="16D32" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="9531"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="NSApplication">
            <connections>
                <outlet property="delegate" destination="Voe-Tx-rLC" id="GzC-gU-4Uq"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customObject id="Voe-Tx-rLC" customClass="AppDelegate">
            <connections>
                <outlet property="mainMenu" destination="AYu-sK-qS6" id="lA2-Ut-v95"/>
            </connections>
        </customObject>
        <customObject id="YLy-65-1bz" customClass="NSFontManager"/>
        <!-- 
        注意：这个XIB文件现在只是一个占位符
        实际的菜单将在代码中动态创建，以支持完整的多语言功能
        可以考虑完全移除这个文件，在代码中创建空的主菜单
        -->
        <menu title="Main Menu" systemMenu="main" id="AYu-sK-qS6">
            <items>
                <!-- 菜单项将在代码中动态创建 -->
            </items>
        </menu>
    </objects>
</document>
