﻿About.Label.OfficialWebsite                                                      =	Official Website
About.Label.PrivacyPolicy                                                        =	Privacy Policy

Account_Biz_Observer_Expired                                                     =	Your Viewer ID has expired. Please contact your company admin

Account_Password_Placeholder                                                     =	Please enter password

Account_PrivacyPolicy_Cancel                                                     =	放弃并退出

Account_PrivacyPolicy_Content                                                    =	<![CDATA[感谢您信任并使用{0}，我们非常重视您的隐私保护和个人信息保护，请您在使用前认真阅读{1}提供的<a href="cn_eula">《用户条款》</a>及<a href="cn_policy">《隐私政策》</a>全部条款。对于您需要重点注意的内容，例如使用本产品会调用的权限及对应用途，以及您使用本产品时享有的权利等，我们已经在协议中进行了重点标注，建议您在操作前仔细阅读。<br/>如您同意上述条款，请点击“同意并继续”开始使用我们的产品及服务。若点击“放弃并退出”，将无法使用我们的产品和服务，并退出本App。]]>

Account_PrivacyPolicy_Continue                                                   =	同意并继续

Account_PrivacyPolicy_Title                                                      =	欢迎使用{0}客户端

Account_Reset_Empty                                                              =	Email cannot be empty

account_signin_toomanytimes                                                      =	Too many password errors. Try again in 10 minutes!

Account_Signup_PasswordEmpty                                                     =	Password cannot be empty

account_user_deleted                                                             =	The account has been removed. Please contact admin

account_user_expired                                                             =	Login expired. Please login again

account_user_kickout                                                             =	Account has logged in from other device

Account_Verification_Error_Verify_Expired                                        =	The verification code has expired, please re-verify

account_verification_warning                                                     =	Your email is not verified. For account security, please verify your email.

ad_payment_LastOrder_bind_num                                                    =	Devices quota {0} (Based on the last order)

ad_scan_retry                                                                    =	Retry

ad_setting_about_feedback_type_binding                                           =	Device binding

add_on_too_low_to_update                                                         =	The device's version of the remote control add-on is too old. You can open the AirDroid Remote Support on the device through the remote control to update the add-on on the info page.

add_on_uninstal_download_again                                                   =	The remote control add-on is uninstalled. Please download it again to use Black Screen Mode.

add_on_was_not_enabledv_1                                                        =	The remote control add-on on the controlled device is not granted accessibility permission

add_on_was_not_enabledv_2                                                        =	The remote control add-on on the controlled device is not granted permission to “Start in the background”

aircast_tv_security_settings                                                     =	Security Settings

airdroid_remote_support_about                                                    =	About AirDroid Remote Support

airdroid_remote_support_hide                                                     =	Hide AirDroid Remote Support

airdroid_remote_support_hide_others                                              =	Hide Others

airdroid_remote_support_minimize                                                 =	Minimize

airdroid_remote_support_preferences                                              =	Preferences

airdroid_remote_support_quit                                                     =	Quit AirDroid Remote Support

airdroid_remote_support_show_all                                                 =	Show All

airmirror_closing                                                                =	Exiting...

airmirror_connect_noscreenshot_resolution2                                       =	Click "Retry" to try again.

AirMirror_Connect_Server_Failed_Resolution                                       =	Please check your network connection then try again

AirMirror_Settings_Bell_Reminder                                                 =	Ring first when making a Voice Call

AirMirror_Settings_Bell_Reminder_Tips                                            =	Controlled device must install the latest version of Biz Daemon

AirMirror_SetVolumn_Down                                                         =	Volume down

AirMirror_SetVolumn_Up                                                           =	Volume up

AirMirror_ShowMoreRemteControls_Button                                           =	View more ways to remote control

airmirror_stream_restart_resolution                                              =	Restart the device or regrant the Non-Root permission

airmirror_stream_start_failed                                                    =	Failed to initiate remote connection service

airmirror_stream_waiting                                                         =	Continue to wait

AirMirror_Support_TwoFinger_Control_Text                                         =	Now you can zoom and move the screen image using the two-finger touch on a non-rooted device.

AirMirror_Support_TwoFinger_Control_Title                                        =	Now supports two-finger touch for unrooted devices!

AirMirror_VerifyCode_Failed_Step1                                                =	1. Network communication abnormality may cause delay in SMS, if you still do not receive the verification code after 10 minutes, please click again to get the verification code.

AirMirror_VerifyCode_Failed_Step2                                                =	2. Please check if the phone charge is overdue or the service is suspended, or the system message is hidden.

AirMirror_VerifyCode_Failed_Step3                                                =	3. If none of the situations above happen and you still can't receive the verification code, please <Link>contact us</Link>

AirMirror_VerifyCode_Get_Failed                                                  =	Can't receive the verification code?

always_close                                                                     =	Keep Disabled

always_inquire                                                                   =	Always

always_open                                                                      =	Keep Enabled

am_ad_template_day                                                               =	%1$d Day(s)

am_airmirror_switch_tip2                                                         =	2. Switch to General Mode.

am_connecting                                                                    =	Connecting device...(General Mode)

am_rs_gesture_rc_content                                                         =	Only for personal. Do not for commercial use.

am_rs_gesture_rc_non_premium_tips                                                =	(Premium feature)

am_screen_connection_failed                                                      =	Sorry, "Screen Mirroring" cannot start. Refer to following steps to retry:

am_setting_screen_qa_ad_webrtc_fail                                              =	Selected image quality is not supported on the current device

am_setting_screen_qa_airmirror345_high                                           =	High

am_setting_screen_qa_airmirror345_low                                            =	Low

am_setting_screen_qa_airmirror345_standard                                       =	Medium

am_setting_screen_qa_selection                                                   =	Image Quality

am_setting_screen_qa_stream_auto_select_hint                                     =	Balance image quality and connection speed based on network conditions.

am_setting_screen_qa_stream_auto_select_mode                                     =	Balanced Mode

am_setting_screen_qa_stream_quality_mode                                         =	High Definition Mode

am_setting_screen_qa_stream_speed_mode                                           =	Speed Mode

am_setting_screen_qa_switched_to                                                 =	Switch to {0}

am_voice_go_premium_content                                                      =	One-Way Audio allows you to listen to device surroundings through the device microphone. Please upgrade to Premium.

am_voice_title                                                                   =	One-Way Audio

ams_fail_content_13                                                              =	Device offline or failed to obtain installation details

App.Button.Update                                                                =	Update
App.Cell.Downloading                                                             =	Downloading
App.Column.CurrentVersion                                                        =	Current version

app_data_retention_option                                                        =	Keep app data

app_launch_settings                                                              =	Auto Launch Settings

app_uninstall_rs_title                                                           =	Uninstall AirDroid Remote Support

AR_camera_not_support_safe_mode                                                  =	Black Screen Mode does not support AR

ardroid_info_title_rs                                                            =	Remote Support

auto_start_on_boot                                                               =	Start AirDroid Remote Support with Windows

bind_diff_company                                                                =	Enrollment cannot proceed. This device has been enrolled in 2 different companies. Should you have any questions, please contact us for more information

biz_camera_error209                                                              =	Failed to stream camera scene, the device does not have camera

biz_camera_error210                                                              =	Failed to stream camera scene, Biz Daemon version is below *******

biz_device_in_safe_mode_tip                                                      =	This device is under maintenance. Do not touch.

Biz_ErrorOfExistMDM                                                              =	Failed to register. The device has been registered with other MDM solutions. Please unregister it before retrying.

biz_modify_device_name_empty                                                     =	Device name cannot be empty

biz_pc_kickout                                                                   =	Account access permission has changed, please re-login

biz_phone_expired_title                                                          =	Service(s) has expired:

biz_reboot_feature_cannot_use_tips                                               =	This feature is only available for AirDroid Business Standard plan or above users. Please upgrade your plan if needed.

biz_rs_guide_des1                                                                =	Your customers will need to install AirDroid Remote Support (iOS/Android) on their mobile devices.

biz_rs_guide_des2                                                                =	Your customers don’t need an account to start, just follow the instruction to grant a few basic permissions and they will be ready.

biz_rs_guide_des3                                                                =	A 9-digit Connection Code will be displayed on the customer's screen. Enter the code on the above page to connect with the customer.

biz_rs_guide_des5                                                                =	You can even use the AR Camera to see the customer’s surroundings and provide real-time instructions.

biz_rs_guide_title1                                                              =	Customer’s app

biz_rs_guide_title2                                                              =	Easy to start

biz_rs_guide_title3                                                              =	Enter Connection Code

Biz_Sand_Studio                                                                  =	© 2025 Sand Studio  All Rights Reserved.

biz_viewer_no_permission_tips                                                    =	You cannot access this feature.

bizc_screen_permission_request_again                                             =	Resend request

bizc_vds_permission_wait_tips1                                                   =	Tick "Don't show again" to facilitate next usage

bizc_vds_permission_wait_title                                                   =	Tap "Start Now" on the remote device

bookmark_edit_finish                                                             =	Done

Business_BulkFile_sdcard_data_nosupport                                          =	The device's system restriction prohibits this action on the external SD card's data directory.

Business_Feedback_logs_title                                                     =	Feedback | Logs

Business_RemoteControl_blacklist                                                 =	The current organization has improper operations and cannot use the remote control function. If you have any questions, please contact us by email: <EMAIL>.

Business_RemoteControl_connect_failed                                            =	Sorry, failed to activate Remote Control, you can: \r\n1. Check the device network. \r\n2. Unlock or wake up your Android device. \r\n3. Restart AirDroid Biz Daemon or change to Non-Root remote control.

Business_RemoteControl_Lock_nosupport                                            =	The controlled device is running below the Android 9.0 system, cannot use this function

Business_RemoteControl_menu_nosupport                                            =	Navigation bar cannot be modified  under current mode

Business_RemoteControl_nosupport                                                 =	Your account haven't purchased Remote Control service, please contact your admin. Please log in again if purchased.

Business_RemoteControl_USB_Accessibility                                         =	Accessibility permission was not granted, and failed to execute remote control. Please enable Accessibility, or you can choose to Root the device for remote control.

Business_RemoteControls_cameraDisable_Tip1                                       =	Failed to activate device camera due to Policy limitations. Please go to [Restrictions] - [Device Function] from Policy on Admin Console and allow the Camera function and then try again

Cast_About_Title                                                                 =	AirDroid Cast for Mac

Cast_About_Win_Title                                                             =	AirDroid Cast for Windows

Cast_CloseScreensharing_Tip                                                      =	Other user has stopped screen sharing

Cast_Disconnected_Tip                                                            =	Connection Interrupted

Cast_MainMenu_About                                                              =	About AirDroid Cast

Cast_MainMenu_Exit                                                               =	Quit AirDroid Cast

Cast_MainMenu_Help                                                               =	{0} Help

Cast_MainMenu_Hide                                                               =	Hide AirDroid Cast

Cast_Microphone_Permissions_Not_Open                                             =	No microphone detected or permission is not granted.

cast_premium_basic                                                               =	Premium: Basic

cast_premium_standard                                                            =	Premium: Standard

Cast_Setting_AutoStartup                                                         =	Launch at startup

Cast_Update_Fail_Text                                                            =	Failed to download the update. You can try again or manually download it from the official website.

Cast_Update_Force_Text                                                           =	Your AirDroid Cast version is too old and no longer supported. Please update to the latest version.

Cast_Update_Now                                                                  =	Close AirDroid Cast and update

Cast_Update_Text                                                                 =	The version you are using is an old one {0} . Update is recommended.

Cast_Update_Title2                                                               =	Update available for AirDroid Cast!

change_sleep_settings                                                            =	Change Sleep Settings

clipboard_content_received_tip                                                   =	Clipboard received

clipboard_content_sent_tip                                                       =	Clipboard sent

commom_get_free_premium                                                          =	Get Free Premium

Common.Abort                                                                     =	Abort
common.autoRenewTime                                                             =	Next automatic renewal: {0} (UTC)
Common.Bind                                                                      =	Device binding
common.bizOrderDetail1                                                           =	Order: {0}
common.bizOrderDetail2                                                           =	Expiration date: {0} (UTC)
Common.Button.Close                                                              =	Close
Common.Button.Refresh                                                            =	Refresh
Common.Button.Save                                                               =	Save
Common.Cancel                                                                    =	Cancel
Common.Click                                                                     =	Visit Online Guide
Common.Copy                                                                      =	Copy
Common.Cut                                                                       =	Cut
Common.Hour                                                                      =	hours
Common.Ignore                                                                    =	Ignore
Common.Info                                                                      =	Notice
Common.Label.Name_new                                                            =	Name
Common.Minute                                                                    =	mins
Common.No                                                                        =	No
Common.NoHint                                                                    =	Don't show this again
Common.OK                                                                        =	OK
Common.OpenFileDialog.PicFile                                                    =	Picture files
Common.Paste                                                                     =	Paste
common.purchase                                                                  =	{0}Purchase{1}
Common.Retry                                                                     =	Retry
Common.SendAndEndLine                                                            =	Press "Enter" to send, "Shift+Enter" to new line
common.sendSalesMail                                                             =	If you have any questions or need assistance, please don't hesitate to reach out to us at {0}<EMAIL>{1}.
common.sendSuccessMail                                                           =	If you have any questions or need assistance, please don't hesitate to reach out to us at {0}<EMAIL>{1}.
common.sendSuccessTeamMail1                                                      =	You can reach us by emailing {0}<EMAIL>{1} or access relevant information from our {2}Help Center{3}.
Common.Yes                                                                       =	Yes

common_accept                                                                    =	Accept

Common_Accessibility_invalid_input                                               =	You are tapping outside of the screen

Common_Accessibility_permission_denied                                           =	Accessibility permission has been disabled, please enable it

Common_Accessibility_Plug_delete                                                 =	The control add-on has been removed, please reinstall it on the remote device

Common_Accessibility_Plug_fail_title_root_fail_airdroid_version_old_tip          =	Please use AirDroid for Win/Mac client to perform the Non-Root setup for the device, and then retry. Or upgrade AirDroid on the device to the latest version to control the device via the control add-on.

Common_Accessibility_Plug_fail_title_root_fail_no_permission_tip                 =	The accessibility permission is not granted, failed to proceed with remote control. Please grant the permission on the device via [Me]-[Security & Remote Features]-[Remote Control]. You can also root the device or use AirDroid for Win/Mac client to perform the Non-Root setup for the device.

Common_Accessibility_Plug_fail_title_root_fail_not_installed_tip                 =	The device is not rooted or the add-on is not installed, failed to proceed with remote control. Please install the add-on on the device via [Me]-[Security & Remote Features]-[Remote Control]. You can also root the device or use AirDroid for Win/Mac client to perform the Non-Root setup for the device.

Common_Accessibility_service_not_started                                         =	Accessibility permission is no longer available on the remote device, please re-enable it.

Common_account_deactivate                                                        =	Access Denied

Common_account_delete_tips                                                       =	The account has been removed

Common_account_Email_Need_Verify                                                 =	*We need to verify your email, please enter a valid one

Common_account_signin_apincorrect                                                =	Account or password error

Common_account_verification_code_empty                                           =	Please fill Verification Code

Common_account_verification_code_input                                           =	Enter Verification Code

Common_account_verification_doing                                                =	Verify now

Common_account_verification_error_invalid                                        =	Invalid verification code

Common_account_verification_error_newmail_same                                   =	Please enter a new email

Common_account_verification_error_newmail_wrong                                  =	Email format invalid, please check

Common_account_verification_error_ofen                                           =	Too many attempts, please try again after 1 minute.

Common_account_verification_error_often                                          =	Too many attempts, try again later

Common_account_verification_exit_confirm                                         =	Confirm to exit

Common_account_verification_exit_continue                                        =	Continue verify

Common_account_verification_exit_text                                            =	You won't be able to create an account if you cancel the verification process

Common_account_verification_exit_title                                           =	Cancel verification

Common_account_verification_expire_warn_title                                    =	For account security, please verify your email as soon as possible. You won't login after {0} if still not finish verify process.

Common_account_verification_modify                                               =	Change verify email

Common_account_verification_modify_empty                                         =	New email and password is not allowed be empty

Common_account_verification_newmail                                              =	New email

Common_account_verification_newmail_tip                                          =	This will be your new AirDroid account (Use the new mail to login later)

Common_account_verification_next                                                 =	Next

Common_account_verification_password                                             =	Confirm password

Common_account_verification_phone_input                                          =	Please input your phone number

Common_account_verification_signin_exit_text                                     =	You will not be able to sign in with this account if you cancel the verification.

Common_account_verification_skip                                                 =	Skip

Common_account_verification_success                                              =	Successfully Verified

Common_account_verification_text                                                 =	We've sent a mail which included verification code to

Common_account_verification_title                                                =	Verify email

Common_account_verification_verify                                               =	Verify

Common_account_verification_warn_title                                           =	For account security, you need verify your email first.

Common_activate_camera_failed                                                    =	Failed to activate camera.

Common_ad_pc_quick_nonroot_exit                                                  =	Are you sure you want to quit Non-Root setup?

Common_ad_permission_voice_content                                               =	Listen to device surroundings through granting Microphone permission, feel like you are right there.

Common_ad_permission_voice_title                                                 =	One-Way Audio

Common_ad_permission_voice_warning                                               =	Microphone is unavailable, tap to enable.

Common_add_button                                                                =	Add

Common_AirDroid                                                                  =	AirDroid Personal

Common_AirDroid_Biz                                                              =	AirDroid Business

Common_AirDroid_Newfeature                                                       =	New Feature

Common_airmirror_openvoice_fail                                                  =	Failed to activate

Common_airmirror_openvoice_retry                                                 =	One-Way Audio is unavailable for General Mode.

Common_Airmirror_title                                                           =	AirMirror

Common_Airmirror_update_tip                                                      =	You need to update the controlled Android client to its latest version to use this feature.

Common_Airmirror_voice_error_tip_1                                               =	Failed to mirror device screen

Common_Airmirror_voice_error_tip_2                                               =	. You can:

Common_Airmirror_voice_error_tip_3                                               =	\r\n1. Click Retry to try again. \r\n2. Switch to General Mode. \r\n(“One-Way Audio” is not supported under General Mode)

Common_Airmirror_voice_error_tip2                                                =	Failed to mirror device screen.\r\nYou can: \r\n1. Click Retry to try again. \r\n2. Switch to General Mode.

Common_Airmirror_voice_error_tip2_biz                                            =	Failed to mirror device screen.\r\nYou can: \r\n1. Click Retry to try again. \r\n2. Switch to General Mode.\r\n（General Mode not support acquire remote sound and Voice Call）

Common_Airmirror_voice_error_tip2_biz_sound                                      =	Failed to mirror device screen.\r\nYou can: \r\n1. Click Retry to try again. \r\n2. Switch to General Mode.\r\n（General Mode not support acquire remote sound）

Common_Airmirror_voice_notsupt_feedback                                          =	Your device (this computer) is not support One-Way Audio. Please feedback to us, we'll help you to solve your problems soon.

Common_Airmirror_voice_notsupt_toast                                             =	“One-Way Audio” is not supported under General Mode.

Common_airmirror_waitconnect_tip                                                 =	Device connection interrupted, wait for reconnecting...

common_all_item                                                                  =	All

Common_am_no_touch_select                                                        =	Don't show again

Common_am_recording_start_tip                                                    =	Start recording

Common_am_recording_stop_tip                                                     =	Stop recording

Common_am_xiaomi_background_err_tips                                             =	Please enable related permission on the controlled device through [Settings] > [Permissions] > [AirDroid] > [Display pop-up windows while running in the background], then retry.

Common_android_ver_nosupt                                                        =	Sorry, Android OS lower than 4.1 is not compatible yet.

Common_AndroidQDontSupportToutch_Tip                                             =	You are using a higher Android version. You may only view your device screen while remote control is unavailable.

Common_app_name                                                                  =	Name

Common_app_size                                                                  =	Size

Common_attention                                                                 =	Attention

Common_back                                                                      =	Back

common_back_again_to_exit                                                        =	Press "Back" again to exit app

common_bf_banner_end_time                                                        =	All Sales End on Dec 1st, 2022 (UTC)

Common_biz                                                                       =	Business

Common_biz_access_limit_dev_connect                                              =	Restricted Access is turned on at the controlled device, to remote access, please tap "Accept" on the device or turn off Restricted Access

Common_biz_access_limit_dev_reject                                               =	Failed to connect the controlled device. Restricted Access is turned on at the controlled device and refused your request

Common_biz_access_limit_dev_timeout                                              =	Failed to connect the controlled device. Restricted Access is turned on at the device and did not answer your request

Common_biz_access_limit_tips                                                     =	The device has enabled Restricted Access

Common_biz_account_deactivate_tips                                               =	Current account has no access to AirDroid Business, to access please contact the Admin or the account Owner

Common_biz_depploy_code                                                          =	Deployment Code

Common_biz_device_amount_exceed                                                  =	The number of devices exceeded the limit. Please go to the Admin Console to unenroll devices and then sign in again here.

common_biz_disable                                                               =	Your business account has been banned, please contact your admin.

Common_biz_feature_expire_tips                                                   =	Service(s) has expired: \r\n{0}Please contact your admin

Common_biz_file_install_nonapk_tips                                              =	Helps you install APK on a remote device. Choose an APK file and then click the icon for APK Remote Install

Common_biz_kiosk                                                                 =	Kiosk

Common_biz_login_SubAccountRevmoed                                               =	Account is removed from {0}. Please contact admin.

Common_biz_OpenAudioPlay_PermissionDenied                                        =	Microphone permission is not granted on the device, failed to acquire remote sound. Please go to Biz Daemon and tap corresponding feature to enable it.

Common_biz_OpenAudioPlay_PermissionDenied_voice                                  =	Microphone permission is not granted on the device, failed to use voice call. Please go to Biz Daemon and tap corresponding feature to enable it.

Common_biz_pay_result                                                            =	Result of purchase

Common_biz_rs_userid                                                             =	User ID: {0}

common_biz_setting_airmirror345                                                  =	General Mode

common_biz_setting_airmirror345_hint                                             =	Faster connection speed, lower frame rate, lower data consumption.

common_biz_setting_connect                                                       =	Connection Mode

common_biz_setting_stream                                                        =	Dynamic Mode

common_biz_setting_stream_hint                                                   =	Compatible for acquiring remote sound of the target device surroundings. To reduce lag, the system will auto-adjust the display quality based on the network conditions.

common_biz_upgrade                                                               =	This feature is only available for the AirDroid Business Standard and Enterprise plans. Please upgrade your plan if needed.

common_biz_upgrade_not_owner                                                     =	This feature is only available for the AirDroid Business Standard and Enterprise plans. Please contact your account owner to upgrade your plan if needed.

Common_biz_viewer_valid_date_err                                                 =	Your viewer ID is not in valid period.

Common_biz_wired_network                                                         =	Wired

Common_bizaccount_expire                                                         =	Account expired&#10;Please contact your admin

Common_blackScreen_about_to_enter                                                =	Entering Black Screen Mode

Common_blackScreen_basic                                                         =	While in Black Screen Mode, the remote device screen will darken, and your remote operations will only be visible to you.

Common_blackScreen_close                                                         =	Turn off Black Screen Mode

Common_blackScreen_closeDescribe                                                 =	After turning off the Black Screen Mode, the remote device screen will return to its normal state.

Common_blackScreen_closeDescribe2                                                =	You are currently in Black Screen Mode, the remote device screen is darkened. If you end the remote session, the Black Screen Mode will be turned off.

Common_blackScreen_closeDescribe3                                                =	You are currently in Black Screen Mode. If you end the remote session, the Black Screen Mode will be turned off.

Common_blackScreen_closeDescribe4                                                =	After turning off the Black Screen Mode, the remote device screen will return to its normal state.

Common_blackScreen_disconnect                                                    =	Sorry, the connection with the device has ended. To ensure the content of your operation is not exposed, the remote device will remain in the Black Screen Mode.

Common_blackScreen_disconnect2                                                   =	Please refer to the following methods to reconnect:\r\n1. Check the remote device network.\r\n2. Wake up or unlock your device.\r\n3. Open AirDroid Biz Daemon on the device.

Common_blackScreen_disconnect3                                                   =	Sorry, the connection with the device has ended due to a network issue. To ensure the content of your operation is not exposed, the remote device will remain in the Black Screen Mode. Please check your network and try again.

Common_blackScreen_enter                                                         =	Entering Black Screen Mode

Common_blackScreen_failed_close                                                  =	Failed to exit Black Screen Mode

Common_blackScreen_failed_close_immediately                                      =	Close

Common_blackScreen_failed_open                                                   =	Failed to turn on Black Screen Mode

Common_blackScreen_failed_opendescribe                                           =	Please enable the Display over other apps permission in the [Remote Control] section of the Biz Daemon on the remote device, and try again.

Common_blackScreen_failed_unknow                                                 =	Please check your current network and remote device network then retry

Common_blackScreen_failed_unknow2                                                =	The remote device screen is darkened while in Black Screen Mode, closing will not exit the Black Screen Mode. You can check the remote device network and try again.

Common_blackScreen_failed_unsupport                                              =	The Black Screen Mode is not supported on this remote device. Please contact us and let us help.

Common_blackScreen_introduction                                                  =	Entering the Black Screen Mode, the remote device screen will darken, and your remote operations will only be visible to you.

Common_blackScreen_now                                                           =	You are in Black Screen Mode

Common_blackScreen_now2                                                          =	The screen is only visible to you

Common_blackScreen_now3                                                          =	Failed to turn on Black Screen Mode

Common_blackScreen_now4                                                          =	The screen is also visible to the remote client

Common_blackScreen_quit                                                          =	Exiting Black Screen Mode

Common_blackScreen_tip                                                           =	Newly added Black Screen Mode

Common_blackScreen_unusual1                                                      =	Due to system limitations, Black Screen Mode cannot be turned on, your remote operations will be visible on the device.

Common_blackScreen_unusual2                                                      =	Returning to Black Screen Mode, remote operations will only be visible to you.

Common_blackScreen_unusual3                                                      =	The device is currently in Black Screen Mode, you can choose to exit, or remain in the Black Screen Mode.

Common_BlackScreenMode_Daemonnosupport                                           =	The current device Biz Daemon version is too low, cannot display the "Do not touch" hint.

Common_BlackScreenMode_NoAccessibility                                           =	Accessibility permission is no longer available on the remote device. Please enable it to use Black Screen Mode.

Common_BlackScreenMode_SingleBox_text                                            =	Display the "{0}" hint on the controlled device (The Owner or Super Admin can customize prompts in the Settings page of the Admin Console)

Common_BlackScreenMode_SingleBox_text_v1                                         =	Display the "{0}" hint on the controlled device (The Owner can customize prompts in the Settings page of the Admin Console)

Common_bluetooth_alwaysoff                                                       =	Keep Bluetooth Disabled

Common_bluetooth_alwayson                                                        =	Keep Bluetooth Enabled

common_brackets                                                                  =	({content})

Common_browser_not_support                                                       =	The current browser is not supported, please upgrade to a higher version or change to Chrome.

Common_browser_not_support_v2                                                    =	Your browser is not supported, please use the Chrome web browser.

common_bulk_transfer                                                             =	Bulk Transfer

Common_BulkFile_fail_test1                                                       =	Failed to download because the requirement was not met within 24 hours

Common_Business_AirMirror_Fail                                                   =	Connection interrupted. You can:\r\n1. Please ensure that the network of the remote device is stable.\r\n2. Unlock or wake up your Android device.\r\n3. Restart AirDroid Biz Daemon.

Common_Business_Control_Disconnect                                               =	Device: {{0}} is still in connection, end the connection?

common_business_product_recommend_title                                          =	AirDroid product for business

Common_Business_RemoteConnect_Noresponse                                         =	No action has been taken for device {0} for a long time. The system will end the remote control session.

Common_Business_RemoteConnect_Noresponse_tips                                    =	Never remind again

Common_camera_permission_disabled                                                =	Failed to start the camera. Please follow the instructions on your device to enable the permission for Remote Camera.

Common_camera_permission_failed                                                  =	Failed to enable Remote Camera, please grant the "Camera" permission on the device.

Common_camera_start_failed_title                                                 =	Failed to access device camera

Common_Cancel                                                                    =	Cancel

Common_cast                                                                      =	AirDroid Cast

Common_cast_Android_end_session                                                  =	The device {0} has ended the connection

Common_cast_waiting_confirm                                                      =	Waiting for the device to share the screen

common_change_btn                                                                =	Change

Common_change_notifypostion_tips                                                 =	Screen Rotation

Common_Chat_Unread_Message                                                       =	{0} unread messages

Common_check_network                                                             =	Please check network

Common_check_read_and_agree                                                      =	Please read and agree to {0}.

Common_clear_all                                                                 =	Clear all

Common_client                                                                    =	For desktop:

Common_close                                                                     =	Close

Common_close_button                                                              =	Quit

Common_Close_test1                                                               =	One or more devices are still in Black Screen Mode: {0}. Do you want to end the connection and exit the Black Screen Mode?

Common_colon                                                                     =	:

Common_comma                                                                     =	,

Common_Complete                                                                  =	Done

Common_Confirm                                                                   =	Confirm

Common_confirm_operate                                                           =	Confirm to execute?

Common_connect_end_title                                                         =	Connection ended

Common_Connect_Fail                                                              =	Failed to connect to the device.

Common_connect_fail_title                                                        =	Connection failed

Common_connect_to_device                                                         =	Connecting to the device...

Common_connect_to_server                                                         =	Waiting for the device response...

Common_connected_tip                                                             =	Connected, you can start chatting now.

Common_connectfrom_tip                                                           =	Your current device is connecting on others client

Common_connection_type_checking                                                  =	Connecting...

common_contact_tip                                                               =	If you have further questions, feel free to contact <NAME_EMAIL>

Common_continue_button                                                           =	Continue

Common_Control_BlackScreenMode                                                   =	Black Screen Mode

Common_Control_NotResponding_Button                                              =	End now

Common_ControlFail_DeviceLockScreen                                              =	You don't have permission to screenshot this device. The device cannot be controlled remotely when its screen is locked. Please keep the screen lock disabled so that you can control the device remotely anytime, anywhere.

Common_copyright_info                                                            =	© 2025 Sand Studio. All Rights Reserved.

Common_copyright_sinapore                                                        =	© 2011-2025 Sand Studio, Singapore

Common_create_code_fail                                                          =	Failed to generate Connection Code, please check network.

Common_currency_tips                                                             =	* All $ prices shown in USD

Common_Dataflow_Insufficient_Title                                               =	Insufficient Remote Data

Common_date_yesterday                                                            =	Yesterday

Common_day                                                                       =	Day

common_day_time                                                                  =	Day(s)

common_delete                                                                    =	Delete

Common_delete_failed_tips                                                        =	Failed to delete

common_deleting_hint                                                             =	Deleting...

Common_device                                                                    =	Device

Common_DeviceInfo_SDCardAvailable                                                =	External SD Card Available Storage

Common_DeviceInfo_SDCardStorage                                                  =	External SD card Storage

Common_devicenum_tips                                                            =	Device (s)

Common_devices                                                                   =	Devices

Common_dialpad_bulk                                                              =	Queued Dialing

Common_dialpad_bulk_bubble_tips                                                  =	Click to start

Common_dialpad_bulk_content1                                                     =	With Queue Dialing, you can import dozens of phone numbers at once. The system will auto-dial them in sequence.

Common_dialpad_bulk_content2                                                     =	##Please input the contact information in the following format. We'll generate a list automatically.\r\n##Format: Phone number (You DO NOT need to input "##" ahead)\r\n##\r\n##Here's an example:\r\n##2703424444\r\n##************\r\n##\r\n##Note: \r\n##Input only one phone number for each line; please enter the next phone number in the next line.\r\n##The format supports: numeric, #, *, -, +, () in the phone number. Other characters will cause that line to be invalid.\r\n##You can start input here:

Common_dialpad_bulk_pc                                                           =	Queued Dialing (PC)

Common_dialpad_call                                                              =	Make call

Common_dialpad_call_cancel                                                       =	Cancel

Common_dialpad_call_end                                                          =	Call ended

Common_dialpad_call_fail                                                         =	Failed to make call

Common_dialpad_calling                                                           =	Calling

Common_dialpad_content                                                           =	Enter the phone number

Common_dialpad_dialing                                                           =	You're currently in a call, failed to make another

Common_dialpad_duration                                                          =	Duartion {0}

Common_dialpad_empty_content                                                     =	Clear all imported phone numbers?

Common_dialpad_feedback_content1                                                 =	Hi, any feedback about dialpad? We're glad to hear the voice from you, we care about your opinions.

Common_dialpad_feedback_content2                                                 =	Tell us your opinion or advice for this feature

Common_dialpad_feedback_error1                                                   =	Tell us more about your opinion

Common_dialpad_feedback_error2                                                   =	Failed to submit, please retry

Common_dialpad_feedback_success                                                  =	Submitted! We'll value and look into it soon.

Common_dialpad_hangup_fail                                                       =	Current device type does not support end the call remotely. Please end the call manually on the device.

Common_dialpad_noconnect                                                         =	No answered

Common_dialpad_nonephone                                                         =	Phone number is empty or invalid

Common_dialpad_nonetwork                                                         =	Failed to connect, please confirm remote device network condition

Common_dialpad_nosupport                                                         =	Current device is unable to make a call

Common_dialpad_permission_title                                                  =	You haven't grant permission of "Phone" or "Display over other apps"

Common_dialpad_Redial                                                            =	Restart Queued Dialing? Previous call logs will be cleared.

Common_dialpad_setting                                                           =	Call interval

Common_dialpad_status                                                            =	Next in queue

Common_dialpad_title                                                             =	Dialpad

Common_dialpad_update_content                                                    =	Remote device's AirDroid version is too low to use dialpad. Please update to the latest version.

Common_dialpad_update_title                                                      =	Update AirDroid to the latest version

Common_dialpad_vip1                                                              =	Dialpad is only available under the local network for free users. If you want to connect your PC and device using different networks, please upgrade to Premium.

Common_dialpad_vip2                                                              =	Queued Dialing is an exclusive feature for Premium users. With Queue Dialing, you can import dozens of phone numbers at once. The system will auto-dial them in sequence.

Common_Disable                                                                   =	OFF

Common_disconnect_tip                                                            =	Disconnected due to other user's network issue.

Common_disconnect_title                                                          =	Disconnected due to other user's network issue. Click "Retry" to connect.

common_discount                                                                  =	Discount

Common_discount_month_tip                                                        =	Monthly subscription

Common_discount_year_tip                                                         =	Yearly subscription

Common_donot_ask_again                                                           =	Don't ask me again

Common_dontshow_tip                                                              =	For convenience, please check "Don't show again".\r\nIf the permission window is not displayed, please relaunch AirDroid.\r\n\r\n Try again in {0} seconds.

Common_download_daemon_auto_deploy_desc                                          =	With the Auto-Enroll package, there is {0}no need to enter{1} the Deployment Code. Devices are enrolled in the {2}Default group{3} automatically.

Common_download_deamon_desc1                                                     =	Download and install the AirDroid Biz Daemon application on the device that needs to be remotely controlled.

Common_download_deamon_standard_desc                                             =	The Basic package enrolls devices into a {0}designated group{1} (different group uses different codes). Enter the {2}Deployment Code{3} after installation to enroll the device.

Common_download_ios_business_trial_tips                                          =	Only available for purchased users

Common_Downloading                                                               =	Downloading...

Common_email_empty_tips                                                          =	Email is empty

Common_email_hi                                                                  =	Hi,

Common_email_not_exits                                                           =	Email doesn't exist

Common_Enable                                                                    =	ON

Common_Enable_1                                                                  =	Enable

Common_every_day                                                                 =	per Day

Common_every_month                                                               =	per Month

common_everyday                                                                  =	Every day

common_exit                                                                      =	Exit

Common_Exit_SafeMode_Button                                                      =	Exit Black Screen Mode

Common_expired                                                                   =	Invalid

Common_export                                                                    =	Export

Common_export_v1                                                                 =	Export

Common_externalSD_Android10_tip                                                  =	Please grant SD card full access permission to AirDroid if you want to make any changes to the files(upload or edit) stored on your SD card. (Open AirDroid on your Android, go to Tools -> Files -> SD Card.)

Common_externalSD_new_allow_button                                               =	Tap "ALLOW"

Common_externalSD_new_new_title                                                  =	Please grant SD card full access permission to AirDroid if you want to make any changes to the files(upload or edit) stored on your SD card.

Common_externalSD_new_tip                                                        =	*Please follow the guide display on your device and tap [SELECT "SD CARD"] if you've tapped "DENY" and ticked "Don't ask again".

Common_failed                                                                    =	Failed

Common_feedback                                                                  =	Feedback

Common_feedback_fail_message                                                     =	Failed to submit. Please try again.

Common_feedback_success_message                                                  =	Feedback submitted. Thanks for Your Feedback!

Common_feedback_title                                                            =	Your Opinion Matters

Common_File_Message_NoSendingDevice                                              =	No distributing device

Common_file_transfer_size_exceed                                                 =	Failed to send. File size cannot exceed 1GB.

Common_FilePush_downloading                                                      =	Downloading

Common_FilePush_notdownloading                                                   =	Command received, downloads will start when the requirement met

common_finish                                                                    =	End

Common_flashlight_non_available_tips                                             =	Failed to turn on flashlight. Flashlight is not available on the controlled device.

Common_flashlight_tips                                                           =	Flashlight

common_friday                                                                    =	Friday

Common_fullscreen_tips                                                           =	Full-Screen Mode

Common_fullstop                                                                  =	.

Common_general                                                                   =	General

Common_get                                                                       =	Get

Common_get_bluetooth_permission_success                                          =	Bluetooth permission granted

Common_get_clent                                                                 =	Download the App

common_go                                                                        =	Go

Common_go_settings                                                               =	Settings

common_go_to_page                                                                =	Go to {0} page

common_google_tip                                                                =	To make a purchase, please make sure Google services are available on your device, and you have logged in to Google Play.

Common_gotit_tip                                                                 =	Got it

common_gottt                                                                     =	OK

Common_history                                                                   =	History

Common_hours                                                                     =	{0} hours

Common_how_set_assist_permission                                                 =	How to grant Accessibility permission?

Common_how_to_delegate                                                           =	How to grant the permission?

Common_how_to_us_rs                                                              =	How to use Remote Support?

Common_info_account                                                              =	Account

Common_info_nextpaytime                                                          =	Next charge date:

Common_input_emoji_error                                                         =	Emoticons not allowed

Common_input_error_required                                                      =	the required field

Common_input_integer                                                             =	Please enter an integer

Common_input_promocode_tip                                                       =	Enter Discount Code here

Common_install_date                                                              =	Date

Common_installed                                                                 =	Installed

Common_iOS_camera_auth                                                           =	Go to Settings > Privacy > Camera to authorize access to camera

Common_iOS_microphone_auth                                                       =	No access to microphone\r\nPlease go to Settings > Privacy > Microphone to enable Microphone permission.

Common_iOS_photos_auth                                                           =	Go to Settings > Privacy > Photos to authorize access to photos

Common_iOS14_photo_access_empty_tips1                                            =	You haven't allow to access your photos, please tap "Settings" at the top of the screen to setup.

Common_iOS14_photo_access_empty_tips2                                            =	Allow access

Common_iOS14_photo_access_more_btn1                                              =	Select more photos/videos

Common_iOS14_photo_access_more_btn2                                              =	Allow access to all photos/videos

Common_iOS14_photo_access_more_des                                               =	Select more photos/videos or go to "Settings" to allow access to all photos/videos.

Common_iOS14_photo_access_title                                                  =	Camera Roll

Common_iOS14_photo_access_top_des                                                =	You've allowed access to select photos. You can add more or allow access to all photos by tapping "Settings".

Common_iOS14_photo_enable_des                                                    =	This lets you share from your camera roll, and enables other features for photos and videos. Tap "Enable" will go to settings to allow access.

Common_iOS14_photo_enable_popup_title                                            =	Allow access to your photos

Common_iOS14_photo_guide_tips1                                                   =	Allow {0} access to all photos. Find photos and videos faster by viewing your entire camera roll.

Common_iOS14_photo_guide_tips2                                                   =	Select photos to limit access. You'll need to manually select new photos every time you want to share.

Common_iOS14_photo_guide_title                                                   =	Access Your Camera Roll to Share Photos

Common_iOS14_photo_no_access_btn                                                 =	Allow access

Common_iOS14_photo_no_access_des                                                 =	No permission to access photos, you can't view and share photos or videos in this app.

Common_Kid_location_time                                                         =	Last updated:

Common_kids                                                                      =	AirDroid Kids

Common_landscape                                                                 =	Landscape Mode

Common_later                                                                     =	Later

common_latest_version                                                            =	You're up to date

Common_leave                                                                     =	Leave

Common_leave_confirm_text                                                        =	Uploading will be cancelled. Are you sure you want to leave?

common_left_bracket                                                              =	(

common_load_failed                                                               =	Load failed

common_load_retry                                                                =	Reload

common_loading                                                                   =	Loading...

Common_local_connect_instability                                                 =	{0} is not in the same LAN, or the LAN is not stable.

Common_lock                                                                      =	Lock

Common_login_browser_not_support                                                 =	Current browser doesn't support third party login,\r\n please use Chrome and set it as system default browser.

Common_login_browser_not_support_tips                                            =	(How to setup?)

Common_login_DeviceVerify_Text                                                   =	You are logging into a new device and will require device verification. A message has been sent to {0} for validation purposes.

Common_login_DeviceVerify_Title                                                  =	Verification Needed

Common_login_fail                                                                =	Failed to sign in

Common_login_RemoteVerify_Text                                                   =	Your account is detected in a different location and requires verification. A message has been sent to {0} for validation purposes.

Common_Logout_test1                                                              =	Device: {0} is still in connection. Do you want to log out and end the connection?

Common_Logout_test2                                                              =	One or more devices are still in Black Screen Mode: {0}. Do you want to log out and end the Black Screen Mode?

Common_menu_tittle                                                               =	Menu

Common_message_upgradedaemon                                                     =	This feature is only supported on the latest version of AirDroid Biz Daemon. Please check for update and retry.

Common_microphone                                                                =	Microphone

Common_minutes                                                                   =	{0} minutes

Common_mobile                                                                    =	For mobile:

common_monday                                                                    =	Monday

Common_month                                                                     =	Month

Common_mute                                                                      =	Mute

Common_name                                                                      =	Name

Common_name_include_sensitive                                                    =	Invalid name, please modify or contact us to verify qualifications.

common_name2                                                                     =	Name

Common_Next_Payment_Info                                                         =	Next automatic renewal: {0}; required payment: {1}

Common_No                                                                        =	No

Common_no_data                                                                   =	No data

Common_no_device                                                                 =	No device

Common_no_empty_tips                                                             =	Content cannot be empty!

Common_no_thanks                                                                 =	No, thanks

Common_none                                                                      =	None

Common_nonroot_setup_again                                                       =	How to regrant the Non-Root permission if the permission is revoked?

Common_NoSupportAndroidQ_Tip                                                     =	You're using a higher Android version, current feature is unavailable on current device.

Common_notify_nolist_tips                                                        =	No message yet

Common_notify_nomore_tips                                                        =	No more content

Common_notify_readmore                                                           =	Read More

Common_notifycenter_tips                                                         =	Notification Center

Common_OK                                                                        =	OK

Common_OK2                                                                       =	OK

common_one_quarter                                                               =	Quarter

Common_only_one_camera_tips                                                      =	Failed to switch camera, the controlled device has only one camera.

Common_OpenAudioPlay_PermissionDenied                                            =	Your device "Microphone" permission is unavailable, please go to "AirDroid > Me > Security & Remote Features" and tap corresponding feature to enable it.

Common_OpenAudioPlay_RemoteAccessDenied                                          =	Your device "One-Way Audio" function is off, please go to "AirDroid-Me-Security & Remote Features" and tap corresponding feature to enable it.

common_operate_success_new                                                       =	Operation successful

Common_optional                                                                  =	(Optional)

Common_or                                                                        =	- or -

Common_packid                                                                    =	Package ID

Common_paid                                                                      =	Paid

Common_password_cannot_same_as_account                                           =	The password cannot be the same as the account.

Common_patent_tip                                                                =	*This feature has been applied for a patent in the US and Europe

common_pause                                                                     =	/

common_pay_agree_terms                                                           =	By clicking on the "{0}" button, you agree to these

Common_pay_card_date                                                             =	MM/YY

Common_pay_discount_code                                                         =	Discount Code

Common_pay_discount_fee                                                          =	Discount

Common_pay_failed_title                                                          =	Payment Failed

Common_pay_fee_sign                                                              =	$ {0}

common_pay_go_to_paypal_des                                                      =	Click "{0}" to pay via PayPal

Common_pay_save_price                                                            =	Save

Common_pay_select_title                                                          =	Please select

Common_pay_successful_validtime                                                  =	Take effect in {0} hours

Common_pay_total_fee                                                             =	Total Price

Common_payment_exceed_maximum                                                    =	Failed to purchase. Exceed the numbers of limit. To purchase more, please contact sales:

Common_payment_failed_tips                                                       =	Payment failed. Please retry

Common_payment_process_tips                                                      =	We're dealing your order, please wait few minutes.

Common_PC_BulkFile_fail8                                                         =	Files have expired and were deleted. Please recreate the task.

common_per_page_item                                                             =	Show {0} per page

common_personal_product_recommend_title                                          =	AirDroid product for individuals

Common_phone                                                                     =	Phone number

Common_please_confirm_uninstall                                                  =	Uninstall Confirmation

Common_please_enter_password_again                                               =	Please enter password again

Common_Policy_Disable_Camera                                                     =	Failed to activate device camera due to Policy limitations. Please exit Policy applied on the device from Admin Console or remove Policy directly on the device to resume using

Common_Policy_Disable_Camera_Tip1                                                =	Failed to activate device camera due to Policy limitations. Please contact your admin and allow the Camera by navigating to [Restrictions]-[Device Function] on the Admin Console and then try again

Common_Policy_Disable_Camera_Tip1_v1                                             =	Failed to activate device camera due to Policy limitations. Please contact your admin to check the device's policy and remove the restrictions on the camera function before trying again.

Common_Policy_Disable_microphone                                                 =	Failed to use the microphone due to Policy limitations. Please contact your admin to allow the microphone by navigating to [{0}] - [{1}] on the Admin Console and then try again

Common_Policy_Disable_microphone_tip1                                            =	Failed to use the microphone due to Policy limitations. Please exit Policy applied on the device from Admin Console or remove Policy directly on the device to resume use

Common_Policy_Disable_Power_Prompt                                               =	Failed to restart the device due to a limitation in the Policy and Kiosk configuration file applied to the device. Please check the Kiosk configuration file on Admin Console and turn off the Disable Power Menu option and try again

Common_Policy_Disable_Power_Prompt_Tip1                                          =	Failed to restart the device due to a limitation in the Policy and Kiosk configuration file applied to the device. Please contact the admin to check the configuration file applied to the device and turn off the switch that prevents the Disable Power Menu from popping up

Common_Policy_Disable_Screen                                                     =	You cannot take a screenshot because this feature is unavailable. Please enable the screenshot function in [{0}] - [{1}] in the Policy of this device, and then try again.

Common_Policy_Disable_Screen_Tip1                                                =	You cannot take a screenshot because this feature is not available. Please disable the Policy applied to the device in the Admin Console or directly from the device.

Common_Policy_Disable_Screen_Tip2                                                =	You cannot take a screenshot because this feature is not available. Please ask your admin to enable the screenshot function in [{0}] - [{1}] in the Policy of this device, and then try again.

Common_portrait                                                                  =	Portrait Mode

Common_Premium_Purchase                                                          =	Upgrade to Premium

Common_pricing_title                                                             =	Pricing

Common_profile_mail_verification                                                 =	Email Verification

Common_ProfilePage_Activated                                                     =	Activated

Common_promocode_submit                                                          =	Apply

Common_Pull_Refresh                                                              =	Pull to refresh

common_pull_up_load_more                                                         =	Pull up to load more

common_pwd_exceed_limit                                                          =	The password is too long

Common_qr_code                                                                   =	QR Code

Common_quotation_left                                                            =	"

Common_quotation_right                                                           =	"

common_rate_us                                                                   =	Rate {0}

Common_read_and_agree                                                            =	I have read and agree to {0}.

Common_readmore_tips                                                             =	Show more history

common_receive_failed                                                            =	Fail to receive

common_receive_success                                                           =	Received

common_receiving                                                                 =	Receiving

Common_reconnect                                                                 =	Reconnect

common_recurring_monthly                                                         =	Monthly

common_recurring_yearly                                                          =	Yearly

Common_refresh                                                                   =	Refresh

Common_Refresh_Failed                                                            =	Failed to refresh, please retry

Common_register_china_policy_agree                                               =	<![CDATA[请您阅读并同意 <a href="cn_eula">AirDroid服务协议</a>和<a href="cn_policy">AirDroid隐私保护政策</a>]]>

Common_register_china_policy_toast                                               =	请您勾选同意用户条款

Common_rejected                                                                  =	Rejected

Common_Release_Refresh                                                           =	Release to refresh

Common_remain_months                                                             =	month(s)

Common_remark_title                                                              =	Remark

Common_RemoteControl_nonroot_login                                               =	Non-Root Setup

Common_RemoteMode_Option                                                         =	Dynamic Mode|General Mode

common_remove_failed                                                             =	Failed to remove

Common_rename_empty                                                              =	Cannot be empty

Common_rename_fail                                                               =	Failed to rename

Common_rename_formatfailed                                                       =	Name cannot include following characters: \ / : * “<> | ‘ & ?

Common_rename_formatfailed_cus                                                   =	Name cannot include following characters: {0}

Common_rename_ios                                                                =	Device rename is not support for iOS devices ()

Common_rename_same                                                               =	The name is already taken

Common_rename_saving                                                             =	Saving...

Common_rename_success                                                            =	Successfully renamed

Common_rename_toolong                                                            =	The device name could not be more than {0} characters.

Common_Renew                                                                     =	Renewal

common_request_rejected                                                          =	Connect request rejected

Common_res_error                                                                 =	Network issue or unknown error, please check your network and try again later

Common_restart                                                                   =	Restart

common_right_bracket                                                             =	)

Common_rom_used                                                                  =	RAM used

Common_RS_AddedSuccessfully                                                      =	Added successfully

Common_rs_blackScreen_disconnect                                                 =	Sorry, the connection with the device has ended. To ensure the content of your operation is not exposed, the remote device will remain in the Black Screen Mode.

Common_rs_blackScreen_disconnect2                                                =	You can try to reconnect with the following steps: \r\n1. Check the network connection of your remote device \r\n2. Wake up or unlock the device \r\n3. Please try opening AirDroid Remote Support on the remote device

Common_rs_blackScreen_version_too_low                                            =	The remote device's version of AirDroid Remote Support is too old. You can search for it in major app stores and update it to the latest by controlling the device.

Common_RS_Business_FileTransfer_Not_Policy                                       =	The organization of the current enrolled device, has disabled the file transfer feature.

Common_RS_Connect_DeviceScreen                                                   =	Obtaining device screen...

Common_RS_DeviceList_refresh                                                     =	Failed to refresh, please retry

Common_rs_emptycode                                                              =	Connection Code cannot be empty, please type a valid code

common_rs_end_phone_btn                                                          =	Hang up

Common_RS_FileTransfer_Not_Policy                                                =	This organization has disabled the file transfer feature.

Common_RS_FrequentClients_InvalidCode                                            =	Connection failed. The Connection Code has been modified by the client. Please contact the client to obtain the latest Connection Code.

Common_rs_gesture_free_user_tips                                                 =	Remote Control is a premium feature, tap [Me] to subscribe the premium service.

Common_rs_gesture_tips                                                           =	Control and guide your family and friends to solve mobile phone problems.

Common_RS_Not_Permission                                                         =	The current device only allows access from the specified organization, and you do not have access permission.

Common_RS_RemoteControl_not_Accessibility                                        =	Accessibility permission is not enabled or is invalid on the remote device.

Common_RS_RemoteControl_Not_Permission                                           =	Failed to acquire device screenshot because the "Display over other apps" permission has not been granted on the controlled device.

Common_RS_RemoteControl_Not_Policy                                               =	The organization of the current enrolled device, has disabled the remote control feature.

Common_RS_RemoteControl_notScreen                                                =	The screen sharing of the remote devices has been paused or blocked. Please reactivate screen sharing.

Common_RS_RemoteControl_Plugin_VersionLow                                        =	The AirDroid Control Add-on version of the controlled device is too low, please download the latest version.

Common_rs_voicecall_hyperlink                                                    =	Voice Call

Common_rs_voicecall_tip                                                          =	Connected, please enable {0} to start chatting

Common_rsname_title                                                              =	Remote Support

common_saturday                                                                  =	Saturday

common_save_btn                                                                  =	Save

common_save_btn_new                                                              =	Save

Common_screenmirror_android10_tip                                                =	Due to system limitations, device with Android 10 and above system cannot skip the pop-up dialogue . Every time you use Screen Mirroring, you need to tap "Start Now" on the phone to successfully activate it.

Common_ScreenshotFail_DeviceLockScreen                                           =	The device cannot access the screen capture permission, and the View Mode cannot be used under the locked screen. You need to set the device to never lock the screen so you can use the View Mode anytime, anywhere.

common_search                                                                    =	Search

Common_seats                                                                     =	{0} Seat(s)

Common_second                                                                    =	{0} Seconds

common_see_details                                                               =	Details

common_semicolon                                                                 =	;

common_send                                                                      =	Send

Common_send_button                                                               =	SEND

common_send_failed                                                               =	Failed to send

Common_send_shortcut_enter                                                       =	Press “Enter” to send

Common_send_shortcut_enter_ctrl                                                  =	Press "Ctrl + Enter" to send

common_send_success                                                              =	Send successful

Common_SendFailLog_Text                                                          =	Send us a failure log to help us improve this feature and enhance your experience.

common_sending                                                                   =	Sending

Common_server_disconnect_tips                                                    =	Failed to connect to the server

Common_Set                                                                       =	Set

Common_Setting_Unbind_Password                                                   =	Please enter password

Common_Setting_UseStream_Button                                                  =	Connection Mode:

Common_settings                                                                  =	Settings

Common_settings_charge_btn                                                       =	Subscribe

Common_settings_left                                                             =	Left:

Common_settings_reset                                                            =	Reset in {0} days

Common_Settings_Stats                                                            =	Data Usage

Common_settings_total                                                            =	Total:

Common_settings_used                                                             =	Used

common_share                                                                     =	Share

Common_share_button                                                              =	SHARE

Common_sign_up_to_login_tip                                                      =	Already have an account? {0}Sign in{1}

Common_signin                                                                    =	Sign in

Common_signin_Google                                                             =	Sign in with Google

Common_signup                                                                    =	Sign up

Common_SignUp_And_SignIn                                                         =	Sign up and sign in

Common_signup_Google                                                             =	Sign up with Google

common_sleep_not_disabled                                                        =	This computer may now enter sleep mode

Common_star                                                                      =	*

Common_stream_cannot_use_audio                                                   =	Failed to acquire remote sound, the device is not Rooted or the Accessibility permission is not granted. If you have completed the related setup, please reconnect. Or you can switch to View Mode to acquire remote sound.

Common_stream_cannot_use_audio_low                                               =	Device is not Rooted, failed to acquire remote sound.

Common_stream_cannot_use_audio_rc                                                =	Please Root your device or switch to View Mode to acquire remote sound.

Common_stream_cannot_use_nonroot_voip                                            =	Device is not Rooted or the Accessibility permission is not granted, failed to enable Voice Call. If you have completed the related setup, please reconnect. Or you can switch to View Mode to use the Voice Call feature.

Common_stream_cannot_use_nonroot_voip_low                                        =	Device is not Rooted, failed to enable Voice Call. Or you can switch to View Mode to use the Voice Call feature.

Common_stream_cannot_use_nonroot_voip_low_below5                                 =	Device is not Rooted, failed to enable Voice Call.

Common_stream_cannot_use_sound                                                   =	You are using Voice Call, failed to acquire remote sound. Please end the call first and try again.

Common_stream_cannot_use_voip                                                    =	You are acquiring remote sound, failed to use Voice Call. Please stop acquiring remote sound first and try again.

Common_stream_not_support_voip_tips                                              =	Failed to enable Voice Call under General Mode, please switch to Dynamic Mode. Dynamic Mode not only supports Voice Call but can also reduce lag by auto-adjusting the display quality based on the network conditions. To switch modes, please reconnect the device.

Common_stream_not_support_voip_title                                             =	Voice Call not supported

Common_stream_voip_callend                                                       =	Call ended

Common_stripe_fail_tip1                                                          =	Reasons for your payment failure can be:

Common_stripe_fail_tip2                                                          =	Incorrect card number or expiration date

Common_stripe_fail_tip3                                                          =	Insufficient funds

Common_stripe_fail_tip4                                                          =	Some card issuer will reject international charges as an anti-fraud measure

Common_stripe_fail_tip5                                                          =	Please try again or contact your card issuer to confirm the specific reason.

Common_stripe_failed_tip                                                         =	Sorry, your payment was declined by your bank.

Common_submit                                                                    =	Submit

common_sunday                                                                    =	Sunday

Common_SwitchToSimple_Button                                                     =	Try General Mode

Common_system_permission_disabled                                                =	Permission Not Granted

Common_take_photos                                                               =	Take photos

common_thursday                                                                  =	Thursday

Common_time_hour_short                                                           =	{0} h

Common_time_minute_short                                                         =	{0} m

Common_time_second_short                                                         =	{0} s

common_tip                                                                       =	Tip

Common_tips                                                                      =	Tips:

common_total_item                                                                =	Total {0}

Common_transfer                                                                  =	File Transfer

common_trial                                                                     =	Trial

Common_Tryagain_button                                                           =	Try again

common_tuesday                                                                   =	Tuesday

Common_unbind_device_tips                                                        =	Disconnect device

Common_under_Android5_rs_unavailable_tip                                         =	The current device is below Android 5.0, remote support is not supported at this time

Common_under_Android5_voice_unavailable_tip                                      =	The current device is below Android 5.0, acquiring remote sound is not supported at this time

Common_unfind_error_retry_tip                                                    =	Unknown error, please try again later

Common_Uninstall                                                                 =	Uninstall

Common_uninstall_app                                                             =	Uninstall app

Common_uninstall_fail                                                            =	Failed to uninstall

Common_uninstalled                                                               =	Uninstalled

Common_uninstalling                                                              =	Uninstalling...

Common_unknow                                                                    =	Unknown

Common_unpaid                                                                    =	Unpaid

Common_Unsubscribe_Recurring                                                     =	Cancel Recurring Billing

common_update                                                                    =	Update

Common_update_button                                                             =	Update

common_update_fail                                                               =	Update failed

Common_update_to                                                                 =	Upgrade to {0}

common_UpdateToAdvanced_title                                                    =	AirDroid Premium Membership Features

common_UpdateToAdvanced_voice                                                    =	One-Way Audio allows you to listen to device surroundings through the device microphone.

common_updating_hint                                                             =	Updating...

Common_Upgrade_Premium                                                           =	Upgrade to Premium

Common_usbnotify_tips                                                            =	Allow USB Notification (Beta)

Common_Use_Limit_Sub_Tip                                                         =	Your access is limited. Subscribe now for full access.

Common_using                                                                     =	In use

common_verification_code                                                         =	Verification code

common_verify_login_btn                                                          =	Verify and sign in

Common_verify_success                                                            =	Verified successfully

Common_version_toolow                                                            =	Version is too low

Common_wait_for_confirm                                                          =	Waiting for confirmation...

Common_WaitMicrophoneAuthorize_Tip                                               =	Waiting to grant Microphone permission...

Common_wallpaper                                                                 =	Wallpaper

Common_web_delete_account_des                                                    =	All bound devices will be UNBOUND, and CANNOT be recovered. Continue?

Common_web_delete_account_tip_biz                                                =	This account is for both personal and business products. You will be unable to access AirDroid and AirDroid Business if you delete this account.

Common_web_delete_account_tip_premium                                            =	You are a Premium member of AirDroid. You will be unable to access AirDroid and lose Premium benefits if you delete this account.

Common_web_delete_account_tip_rs_premium                                         =	This account is for both personal and Remote Support products. You will be unable to access AirDroid and AirDroid Business if you delete this account.

Common_web_delete_account_warning                                                =	Attention!

Common_web_no_suuport_tip_des                                                    =	Sorry, this feature is currently unavailable on the web version. We recommend accessing and experiencing the full suite of features in the App.

Common_web_no_suuport_tip_title                                                  =	Please access this feature in the App

common_web_resource_load_fail_tip                                                =	Verification failed. Please refresh the page and try again

common_wednesday                                                                 =	Wednesday

Common_wifi                                                                      =	Wi-Fi

Common_wifi_hidenetwork                                                          =	Hidden wireless network

Common_wifi_name                                                                 =	Wi-Fi name

Common_wifi_safe_type                                                            =	WPA/WPA2 PSK

Common_wifi_selectstatus                                                         =	Select Wi-Fi status

Common_wifi_setting                                                              =	Wi-Fi

Common_wifi_status_tips                                                          =	Wi-Fi status

Common_wrong_system_time_and_go                                                  =	The time is set incorrectly, please reset the system time to the correct one!

Common_wrongurl_tips                                                             =	Please enter a valid URL

Common_year                                                                      =	year

Common_Yearly_Sub_Order                                                          =	Annual Recurring Order

Common_Yes                                                                       =	Yes

Common_YTBvideo_play                                                             =	How It Works?

Content_Cannot_Empty                                                             =	Cannot be empty

Content_copied_to_clipboard_tip                                                  =	Copied to clipboard

current_sleep_timer_template                                                     =	This Mac is set to sleep after {0}{1}{2}{3}. (The display is currently currently set to stay on and won’t turn off automatically)

currently_set_to_disable_sleep                                                   =	This computer is set to never sleep

currently_set_to_sleep_after_{0}{1}                                              =	This computer is set to sleep after {0} {1} {2} {3}

daemon_policy_disable_microphone_tip                                             =	Failed to use the microphone due to Policy limitations. Please go to [{0}] - [{1}] to check and allow the microphone function and then try again

data_update                                                                      =	Saved

delectAccoutPop1                                                                 =	Caution! Your AirDroid Business account will also be deleted.

delectAccoutPop2                                                                 =	All bound {0} device(s) will be UNBOUND, and CANNOT be recovered.

delectAccoutPop3                                                                 =	You will NOT be able to access remote support services anymore.

delectAccoutPop4                                                                 =	You are【{0}】Premium member.\r\nDeleting the account will make you unable to access any of AirDroid's services anymore. The account cannot be restored once deleted.

delectAccoutPop5                                                                 =	You are【{0}】Premium member and own business identity. \r\nDeleting the account will make you unable to access any of AirDroid's services anymore. The {1} enrolled device(s) will be unenrolled and cannot be restored once the account is deleted.

delectAccoutPop6                                                                 =	You are【{0}】Premium member and own business identity. \r\nDeleting the account will make you unable to access any of AirDroid's services anymore. The account cannot be restored once deleted.

delectAccoutPop7                                                                 =	Are you sure to continue?

deleteAccount_delete_tip_3                                                       =	Once the account is deleted, you will not be able to use that account to log in to all AirDroid products, including AirDroid Business, AirDroid Remote Support, and GoInsight.AI.

detection_error_notexist                                                         =	{0} doesn't exist

Device_Module_AirMirror                                                          =	Control

device_monitor_device_num                                                        =	Device Quantity

Device_unenrolled                                                                =	Device unenrolled

device_wall_info22_new                                                           =	All Devices

device_wall_info55                                                               =	The account has expired. Try to download after purchase.

disable_sleep_in_power_management_settings                                       =	This device's sleep function is enabled, which may prevent others from connecting to it when it sleeps. Please set the device to never sleep in the Power & Sleep settings.

dlg_input_psw_error                                                              =	Password is incorrect

Download.Cell.DoenloadCompleted                                                  =	Download complete.

download_fail_tip_new                                                            =	If the download doesn't start, please re-click the button

end_connection_close_secure_mode                                                 =	End connection and deactivate Black Screen Mode

enter_8_to_20_characters                                                         =	Enter 8-20 characters

eula_policy                                                                      =	EULA Policy

fail_delete_account                                                              =	Failed to delete account, please try again later

feedback.empty_content                                                           =	Please enter a message.
feedback.empty_email                                                             =	Please enter email
feedback.help                                                                    =	Help Center

feedback_export_log                                                              =	Export log file to…

File.Label.Downloading                                                           =	Downloading : {0}
File.Label.FileTransfer                                                          =	File Transfer
File.Message.EmptyMessage                                                        =	Content cannot be empty
File.Message.FileNotExisted                                                      =	File does not exist

File_Download_MenuItem_Title                                                     =	Download

geo_fence_added_tips_new                                                         =	Fence: {0} created

group_emit_file_info3                                                            =	Download

has_synchronized_clipboard                                                       =	Clipboard synchronized

help_center_title                                                                =	Help Center

installer_welcome_message                                                        =	The installer will guide you through the steps required to install this software.

keep_computer_awake                                                              =	Keep The Computer Awake

keyboard_is_disabled                                                             =	AirDroid Add-on Keyboard deactivated

keyboard_is_enabled                                                              =	AirDroid Add-on Keyboard activated

kid_browser_main_page_text                                                       =	Homepage

kid_download_icon_not_found                                                      =	Already installed AirDroid Kids but can't find the icon?

kid_download_modal_desc                                                          =	The AirDroid Kids icon will be hidden after the installation, you can return to this page to enable AirDroid Kids.

kid_installed                                                                    =	Please ensure that AirDroid Kids is installed on this device. ({0}Tap to download AirDroid Kids{1})

Kids_Age_Plural                                                                  =	{0} years old

Kids_Name_Illegal                                                                =	The name cannot include special characters or emoji

kiosk_bluetooth_connect_failed                                                   =	Failed to connect to {0}

kiosk_dont_use                                                                   =	Skip

lg_bind_success                                                                  =	Successfully connected.

lg_many_mistakes_failed                                                          =	Too many password errors. Try again in 10 minutes!

Login.Button.Exit                                                                =	Exit
Login.Label.PasswordEmpty                                                        =	Please enter password

login_expired                                                                    =	Login data expired. Please login again

Main.Message.CheckingUpgrade                                                     =	Checking for updates...
Main.Message.DownloadingNewVersion                                               =	Downloading the update...
Main.Message.NetworkDisconnectCanNotUpgrade                                      =	Failed to update. Please check your network and retry.
Main.Message.UpdatedNewVersion                                                   =	{0} is up to date.
Main.Message.UpdateNow                                                           =	AirDroid is running. Continue with the update will force close it.
Main.Upgrade.Button.Ignore                                                       =	Skip this update
Main.Upgrade.Button.UpgradeAtOnce                                                =	Update now
Main.Upgrade.Button.UpgradeLater                                                 =	Later
Main.Upgrade.Label.ForceUpgrade                                                  =	The currently installed version is no longer supported. Please update to the latest version to continue.
Main.Upgrade.Label.GetLogFailed                                                  =	Failed to load the update log.
Main.Upgrade.Label.UpgradeInfo                                                   =	Hooray! A new version is available now!\r\nStill using {0}? Update to enjoy a better AirDroid!
Main.Upgrade.Text                                                                =	update available

Main_AddDevice_Or                                                                =	or

Main_Code_Expire                                                                 =	Verification code is expired.

Main_Update_Text_Latest_Version                                                  =	Please update AirDroid on {0} to the latest version to enable this feature.

MainMenu_Update                                                                  =	Check for Update

month_noplan_key                                                                 =	a month plan

month_plan_key                                                                   =	monthly plan

need_more_help                                                                   =	Need more help?

Network_Error_Retry                                                              =	Network error. Please check and retry

never_no_lock                                                                    =	Keep Awake

new_device_enroll                                                                =	New device enrolled: {0}

new_device_unenroll                                                              =	{0} successfully unenrolled.

nickname_format_valid_tip                                                        =	The nickname should not contain: / \ : ? * “ <> | ‘ & airdroid

No_Alert                                                                         =	No alerts yet

no_device_enrolled                                                               =	No device enrolled

No_Promotion                                                                     =	No announcements yet

No_Request                                                                       =	No requests yet

no_special_char                                                                  =	Invalid characters: / \ : * < > | ? ' " = ; , &

none_setting                                                                     =	None

not_support_connect_other_contries_devices                                       =	Connection to a device in other countries is not supported during the trial period.

Note_Label_new.Friday                                                            =	Friday
Note_Label_new.Monday                                                            =	Monday
Note_Label_new.Saturday                                                          =	Saturday
Note_Label_new.Sunday                                                            =	Sunday
Note_Label_new.Thuresday                                                         =	Thursday
Note_Label_new.Tuesday                                                           =	Tuesday
Note_Label_new.Wednesday                                                         =	Wednesday

notification_permission_request_dialog_msg                                       =	To provide better service and experience for you, please enable notification permissions in your device settings.

notification_permission_request_dialog_title                                     =	Enable Notifications

open_RemoteSupport_scan_coad_join_company                                        =	Open AirDroid Remote Support, click {0} for Android devices, and scan the QR code below or enter the Deployment Code to enroll the device in your organization.

operation_new                                                                    =	Operation

option_alt_command                                                               =	Send Option+Command+Esc to the remote Mac device

Parent_Authorization                                                             =	Identity verification

Parent_Delete_Account_Verify                                                     =	Please enter your password below to verify your identity. Deleting the account will make you unable to access any of AirDroid's services anymore. The account cannot be restored once deleted.

Parent_Device_Screenshot_Title                                                   =	Screen Mirroring

Parent_Device_Switch                                                             =	Switched successfully

Parent_Profile_Delete_Account                                                    =	Delete account

Parent_TodayEvent_Notification_Count2                                            =	{0} received

Parent_TodayEvent_Usage_Time_Minute                                              =	min

pay_country_region                                                               =	Country/Region

pay_coupon_error_1                                                               =	Invalid code, please type a correct one

pay_coupon_error_10                                                              =	The coupon cannot be used at the moment since your account is already in the premium service, thanks for your understanding!

pay_coupon_error_2                                                               =	The code has been used

pay_coupon_error_3                                                               =	The code is expired

pay_coupon_error_4                                                               =	This coupon is not available for the selected item

pay_coupon_error_5                                                               =	Please do not enter special characters

pay_coupon_error_6                                                               =	The coupon is not available for your account

pay_coupon_invalid_payment_tips                                                  =	This coupon is not available for the selected payment

pay_coupon_submit                                                                =	Apply

pay_coupon_success_1                                                             =	Successfully redeemed, you'll have a {0} discount!

pay_coupon_tips                                                                  =	* This coupon applies to the current subscription period only. Subscriptions auto-renew at the regular price.

pay_coupon_title                                                                 =	I have a coupon

pay_devices                                                                      =	Devices

pc_biz_intro_des_1                                                               =	Define Multiple Devices Management in a Better Way

pc_biz_intro_des_2                                                               =	AirDroid Business is an efficient, secure, and fast management solution for Android-based devices. It helps business or IT professionals to remotely manage large quantity devices by a centralized approach.

pc_biz_intro_feature1_des                                                        =	Devices can be controlled remotely without granting Root permssions, Non-Root setup, or other complicated steps.

pc_biz_intro_feature1_title                                                      =	Remote Control

pc_biz_intro_feature2_des                                                        =	Application Management Services (AMS) is a suite of tools which enables administrators to provide app updates, releases, and maintenance.

pc_biz_intro_feature2_title                                                      =	AMS

pc_biz_intro_feature3_des                                                        =	Turn your Android devices to become purpose-specific Kiosk to provide better security and productivity for your business.

pc_biz_intro_feature3_title                                                      =	Kiosk Mode

pc_biz_intro_feature4_des                                                        =	You can track the current location and path history of any vehicle or delivery personnel, set up geofences, and automatically trigger workflows and alerts when the object enters or exits the area.

pc_biz_intro_feature4_title                                                      =	Geofencing

pc_intro_common_des_3                                                            =	Visit our website for more feature introduction.

pc_rs_intro_des_1                                                                =	To See and To Assist, in Real-time

pc_rs_intro_des_2                                                                =	AirDroid Remote Support software helps technicians visualize remote issues and guide on-site personnel with clear instructions that phone calls or emails can't provide. The perfect remote troubleshooting tool for resolving mobile and technical issues in the field.

pc_rs_intro_feature1_des                                                         =	You can pinpoint the issues quickly and guide your customers by placing 3D markers onto real-world objects.

pc_rs_intro_feature2_des                                                         =	Request to see the device screen and quickly discover the issue at hand.

pc_rs_intro_feature3_des                                                         =	Swipe or tap while viewing the shared screen, the gestures will show on the customers' device. Easy for anyone to follow and solve the problem.

pc_rs_intro_title                                                                =	AirDroid Remote Support

permission_dialog_allow                                                          =	Allow

permission_dialog_deny                                                           =	Deny

personal_phone_check_tip_content                                                 =	According to the provisions of the "People's Republic of China Network Security Law", if you want to use our services within China, you must bind a Chinese mobile phone number to {0} account to complete verification.

phone_valid_deadline_tip                                                         =	Please complete the phone number verification before {0}

phone_valid_have_valid_v1                                                        =	Verified

phone_valid_no_number_tip_v1                                                     =	* If you have any questions, please <link>contact us</link> as soon as possible to ensure your regular usage.

phone_valid_skip_tip_v1                                                          =	You can choose <link>not to verify for now</link>. Please be sure to verify before {0} so as not to affect your regular usage.

phone_valid_switch_account_v1                                                    =	Sign out

phone_valid_tip_content_v1                                                       =	According to the "Cybersecurity Law of the People's Republic of China" to use the services we provide in China, you must complete the verification process by binding your China mobile phone number to your AirDroid Business account. Thank you for your cooperation!

phone_valid_title_v1                                                             =	Phone Number Verification

phone_valid_to_valid_v1                                                          =	To verify

Photos_iCloud_Download                                                           =	The file is stored in the cloud, tap preview to download.

plan_basic_name                                                                  =	AirDroid Business Basic

plan_enterprise_name                                                             =	AirDroid Business Enterprise

plan_pro_name                                                                    =	AirDroid Business Standard

policy_communicate_answer_call                                                   =	Incoming Calls

Popup.Button.Send                                                                =	Send

pricing_almost                                                                   =	Almost

pricing_buy_more                                                                 =	Buy More

pricing_get_more_help                                                            =	Get More Help

pricing_limit_time_offer                                                         =	Limit time offer

pricing_more_than                                                                =	More than

pricing_most_popular                                                             =	Most Popular

pricing_order_success_btn                                                        =	Go Admin Console

pricing_order_success_desc                                                       =	Transactions usually take a few minutes to be reflected in your account. After that your account will be upgraded to:

pricing_order_success_desc_bundle                                                =	Transactions usually take a few minutes to be reflected in your account. After that your account will be upgraded to:

pricing_order_success_title                                                      =	Upgrade Successful

Privacy_Policy                                                                   =	Privacy Policy

privatization_adddevice_text                                                     =	Please bind devices in Devices page of the Admin Console

Profile_Feedback_TextViewHoder_common                                            =	Enter your queries or suggestions (At least 10 characters)

Profile_NickName_ParameterError                                                  =	Parameter error

Puchase_speical_plan2_contact_sales                                              =	The number of available devices in the add-on feature is different from the device number of current plan. To upgrade or renew your plan please contact our sales: {0}

pwd_exceed_limit                                                                 =	Password cannot exceed 48 characters

quarter_noplan_key                                                               =	a quarter plan

quarter_plan_key                                                                 =	quarterly plan

Remote_BIZ_Enroll                                                                =	Enroll This Device

Remote_Fail_Screen_lock                                                          =	This feature is unavailable while the remote device is locked. Please unlock it and ensure a stable network connection before trying again.

Remote_Support_support_manipulation                                              =	The feature of two-finger touch is now supported by Remote Support.

Remote_Support_version_too_low                                                   =	The AirDroid Remote Support version on the controlled device is too old

RemoteSupport.Button.AddVoice                                                    =	Voice Message
RemoteSupport.Button.Voip                                                        =	Voice Call
RemoteSupport.Label.ConnectingVoIP                                               =	Awaiting response...
RemoteSupport.Message.ConnectEnded                                               =	Connection ended
RemoteSupport.Message.FolderCannotUploadToCloud                                  =	Sending folder is not supported yet
RemoteSupport.Message.InRecording                                                =	Microphone is in use, cannot use Voice Message.
RemoteSupport.Message.RecordingVoice                                             =	Voice Messaging, record up to 60 sec. Tap "√" button to send.
RemoteSupport.Message.SingeFileSpaceNotEnough                                    =	File size cannot exceed {0}
RemoteSupport.Message.VoiceTooShort                                              =	Message too short
RemoteSupport.Message.VoIPCanceled                                               =	Voice Call cancelled by caller
RemoteSupport.Message.VoIPFinished                                               =	Voice Call ended, duration {0}
RemoteSupport.Message.VoIPSuccess                                                =	Voice Call connected
RemoteSupport.Message.VoIPTimeout                                                =	Connection Timed out
RemoteSupport.Message.VoIPWhileRecording                                         =	Microphone is in use, cannot use Voice Call.

Request_Permission_Des                                                           =	To better your communication with clients, may we suggest enabled following permissions

result_info_fail2                                                                =	Bank account was charged but failed to activate {0}? It may be a result of network delay. Please don’t worry. We’ll restore it as soon as possible.

result_info_fail3                                                                =	Bank account was not charged? You can try to make the payment again. If the payment still not succeed, please contact your bank.

result_info2                                                                     =	The order has been successfully placed, transactions usually take a few minutes to be reflected in your account. The order you purchased:

result_info4                                                                     =	Contact us at {0} if needed.

result_info5                                                                     =	Transactions usually take a few minutes to be reflected in your account. After that your account will be upgraded to Business. Capacity for current account is {0} devices.

result_info6                                                                     =	Transactions usually takes a few minutes to reflect in your account. You can check your renewal at【Admin Console】-【Settings】-【Orders】. Your renewal order:

result_info7                                                                     =	If changes do not take effect, please check your bank account for any deduction or contact us at {0}.

rs_add_device_attended_tips                                                      =	When you need to manage the devices added to your organization while using AirDroid Remote Support, please add the device through the {0} method.

rs_add_device_common_tips                                                        =	Open AirDroid Remote Support, click {0} for Android devices, click {1} for iOS devices, and scan the QR code below or enter the Deployment Code to enroll the device in your organization.

rs_add_device_frequent                                                           =	Frequent Clients

rs_add_device_frequent_tips_1                                                    =	When you need to reach your frequent clients and provide services to them conveniently, please add the clients' devices by {0}.

rs_add_device_frequent_tips_2                                                    =	Please enter the 9-digit Connection Code in AirDroid Remote Support on frequent clients' devices.

rs_add_device_unattended_tips                                                    =	If you need to remotely control the devices without assistance anytime and anywhere, please add the device through the {0} method. (Only available for Android devices)

rs_alert_leave_chat                                                              =	Confirm end session?

rs_alert_pic                                                                     =	Photo

rs_allow_access                                                                  =	Allow access

rs_ar_2d_connected_tips                                                          =	AR Camera (2D compatibility mode) is in use.

rs_ar_2d_reject                                                                  =	AR Camera (2D compatibility mode) declined by user

rs_ar_2d_reject_request                                                          =	AR Camera (2D compatibility mode) declined

rs_ar_biz_version_too_low                                                        =	Failed to activate 2D compatibility mode, the supporter's application version is too low.

rs_ar_camera_permission_request_des                                              =	To enable AR Camera and record AR Tutorial

rs_ar_camera_permission_request_title                                            =	Camera

rs_ar_camera_tittle                                                              =	AR Camera

rs_ar_clear_arrow                                                                =	Delete all arrows?

rs_ar_drop_arrow_tips                                                            =	Only your supporter can place arrows

rs_ar_exit                                                                       =	Exit AR Camera?

rs_ar_exit_disconnect                                                            =	End session as well

rs_ar_exit_ios_disconnect2                                                       =	Exit and end session

rs_ar_guide_drop_arrow                                                           =	Tap the screen to place an arrow

rs_ar_guide_seek_anchor                                                          =	Gently pan your device to identify the surroundings

rs_ar_ios_camera_no_camera_permission_title                                      =	Failed to enable the camera. \r\nPlease go to [Settings] > [Camera] and enable.

rs_ar_lowlight_tips                                                              =	Move your device to detect light

rs_ar_marks_undo                                                                 =	Undo

rs_ar_move_device                                                                =	Move your device to another area

rs_ar_not_support_tip                                                            =	Your device does not support AR, 2D compatibility mode will be applied.

rs_ar_not_support_tip_link                                                       =	Learn More

rs_ar_privacy_des                                                                =	AR Camera (including 2D compatibility mode) will enable a voice call using your microphone. Continue?

rs_ar_privacy_title                                                              =	We Care About Your Privacy

rs_ar_reject_request                                                             =	AR Camera declined

rs_ar_tooclose                                                                   =	Too close to identify, please ask your client move back a little

rs_ar_video_attention_tips                                                       =	* AR Camera is only available for business version.

rs_ar_video_attention_title                                                      =	You can use AR Tutorial to record the problem, and place 3D markers to help your supporter better understand the situation.

rs_ar_video_insufficient_storage                                                 =	Insufficient storage available, recording stopped

rs_ar_video_saved_failed                                                         =	Failed to save

rs_ar_video_saved_success                                                        =	Save successfully

rs_ar_video_saving                                                               =	Saving videos

rs_ar_video_title                                                                =	Create AR Tutorial

rs_arrow_drop_failed                                                             =	Failed to drop arrow, please ask your client

rs_attached_mode                                                                 =	Attended

rs_auth_denied_tips                                                              =	Access has been denied

rs_auth_denied_title                                                             =	Failed to connect with device

rs_auth_in_used_tips                                                             =	The device is accessed by "%1$s". Please try again later

rs_auth_tips                                                                     =	Please ask your partner to tap "Accept" on the device to allow the connection.

rs_auto_update_deploy_code_hint                                                  =	Automatically change the device's 9-digit Connection Code at the end of each remote support session

rs_automatically_lock_after_controll                                             =	Automatically lock this device after disconnecting

rs_based_needs_choose                                                            =	Based on your business needs, please evaluate whether this device is <bold>attended and used by personnel</bold> or is deployed remotely, <bold>unattended and not used by personnel</bold>?

rs_bind_code_invaild                                                             =	Invalid Deployment Code

rs_biz_access_removed_tips                                                       =	Your access to the Remote Support feature has been removed. If you have any questions or would like to regain permission, please contact your account owner.

rs_biz_ar_2d_unsupport                                                           =	Your customer's device does not support AR, but you can still see your customer's surroundings through the device camera under 2D compatibility mode.

rs_biz_ar_drop_arrow_failed                                                      =	Failed to drop arrow due to can't recognize the objects.

rs_biz_ar_installing                                                             =	The customer is installing ARCore the application, please wait...

rs_biz_ar_leave_app_tips                                                         =	The customer has left the Remote Support app.

rs_biz_ar_leave_app_tips2                                                        =	Screen transmission will resume when the app is opened again.

rs_biz_ar_reject                                                                 =	AR Camera declined by user

rs_biz_ar_tooclose                                                               =	Too close to identify, please remind your customer to move back a little

rs_biz_ar_unsupport                                                              =	Your customer's device does not support AR, please try the Screen Sharing instead.

rs_biz_ar_voicecall_tips                                                         =	Note: Voice Call is on while AR Camera is enabled

rs_biz_cancel_voicecall                                                          =	Voice Call is cancelled

rs_biz_client_rs_version_too_low                                                 =	Your customer's device does not support AR, and the client app version is too low. To use 2D compatibility mode, please remind your customer to update.

rs_biz_client_ver_toolow                                                         =	Current version of Remote Support does not support AR, please remind your customer to update.

rs_biz_code_history_delete_all                                                   =	Clear all history?

rs_biz_code_history_nocode                                                       =	No history

rs_biz_connected_rotate_error_tip                                                =	This feature is only available after activating Screen sharing or AR Camera

rs_biz_enable_tip                                                                =	Ask your partner to enable...

rs_biz_exit_ar                                                                   =	Exit

rs_biz_expired_cannot_conenction                                                 =	Your {0} service has been expired, please contact the Owner to make a purchase.

rs_biz_expired_connenction                                                       =	Your {0} service has expired. To purchase, please go to Admin Console.

rs_biz_gesture_addon_calling_tip                                                 =	Making Voice Call

rs_biz_gesture_content                                                           =	Guide the user to follow specific actions by showing a gesture. (No direct control of the device)

rs_biz_gesture_unavailable                                                       =	This feature is not supported under AR mode

rs_biz_gesture_version_toolow                                                    =	The Remote Support version on the remote device is too low for remote control. Please remind the user to update.

rs_biz_gestureplugin_call_tips                                                   =	Initiate a Voice Call

rs_biz_gestureplugin_closealert_title                                            =	Quit Remote Control?

rs_biz_gestureplugin_content                                                     =	Control the remote device directly to complete the actions.

rs_biz_gestureplugin_Install_tips                                                =	Please guide the user on the remote device to install AirDroid Control Add-on

rs_biz_gestureplugin_lessthan_SDK7                                               =	Remote Control is unavailable. The system on the remote device is below Android 7.0.

rs_biz_gestureplugin_wating_authorization                                        =	Waiting for permission authorization

rs_biz_gestureplugin_wating_confirm                                              =	Waiting for confirmation

rs_biz_gestureplugin_wating_download                                             =	Waiting for AirDroid Control Add-on download

rs_biz_hours_usedup                                                              =	The Remote Support connection hours for this month have been used up,[)(]

rs_biz_hours_usedup_member                                                       =	please contact the Owner to make a purchase.

rs_biz_hours_usedup_owner                                                        =	to purchase, please go to the Admin Console.

rs_biz_hours_using_up_title                                                      =	Connection hours are about to be used up

rs_biz_hours_usingup                                                             =	The Remote Support connection hours for this month are about to be used up. The remaining time is less than {0}.[)(]

rs_biz_hours_usingup_member                                                      =	If you need more connection hours, please contact the Owner to purchase additional hours.

rs_biz_hours_usingup_owner                                                       =	If you need more connection hours, please go to the Admin Console to purchase additional hours.

rs_biz_loading_2d_failed_tips                                                    =	Failed to capture camera image, please exit and retry.

rs_biz_loading_2d_image                                                          =	Capturing camera image

rs_biz_loading_ar_image                                                          =	Capturing AR Camera image

rs_biz_loading_failed_tips                                                       =	Failed to capture AR Camera image, please exit and retry.

rs_biz_login_not_invited_tips                                                    =	This user is not invited as a member of Remote Support

rs_biz_pc_ar_anchor_tips                                                         =	Reference Point

rs_biz_pc_copy_link                                                              =	Copy download link

rs_biz_pc_copy_link_success                                                      =	Copied successfully

rs_biz_pc_download_apk                                                           =	Download Android APK

rs_biz_pc_scan_qr_code                                                           =	QR code

rs_biz_screenshot_failed                                                         =	Sending failed\r\nPlease retry from the below window

rs_biz_screenshot_process                                                        =	Sending({0})...

rs_biz_screenshot_sucess                                                         =	Sending completed

rs_biz_seats_exceed                                                              =	Remote Support seat number exceeded,[)(]

rs_biz_seats_exceed_member                                                       =	please contact account owner to remove exceeding seat(s) from the Admin Console.

rs_biz_seats_exceed_owner                                                        =	please remove exceeding seat(s) from the Admin Console and sign in again.

rs_biz_swtich_ar_tips                                                            =	Switch to AR Camera

rs_biz_time_usedup_tips                                                          =	Remote Support service available time used up, please contact your account owner.

rs_biz_wait_anchor                                                               =	Identifying device surroundings, please wait...

rs_bound_devices_limit_to_purchase                                               =	The number of devices enrolled in this organization exceeds the limit. To enroll more unattended devices, please contact the Owner.

rs_brush_using                                                                   =	Brush is in use, please try again later.

rs_business_connect_title                                                        =	A support specialist requests to connect with you

rs_call_decline                                                                  =	Voice Call declined by your partner

rs_change_connection_password                                                    =	Change

rs_chat                                                                          =	Chat

rs_chat_ar_2d_disabled                                                           =	AR Camera (2D compatibility mode) closed

rs_chat_ar_2d_enabled                                                            =	AR Camera (2D compatibility mode) enabled

rs_chat_ar_2d_request_initiate                                                   =	You sent a request for AR Camera (2D compatibility mode)...

rs_chat_ar_2d_request_recieve                                                    =	Your supporter requests to enable your camera

rs_chat_ar_closed                                                                =	AR Camera closed

rs_chat_ar_enabled                                                               =	AR Camera enabled

rs_chat_ar_request_initiate                                                      =	You sent a request for AR Camera...

rs_chat_ar_request_recieve                                                       =	Your supporter requests to enable your AR Camera

rs_chat_broadcast_open                                                           =	Screen sharing started

rs_chat_broadcast_pause                                                          =	Screen sharing paused

rs_chat_broadcast_resume                                                         =	Screen sharing resumed

rs_chat_broadcast_stop                                                           =	Screen sharing suspended

rs_chat_file_exisit_icloud                                                       =	Files stored on iCloud. To preview, please go to iCloud.

rs_chat_file_limit_size                                                          =	Can't transfer file size over 1GB

rs_chat_input_placeholder                                                        =	Type here

rs_chat_message_chat_tip                                                         =	Start chatting with {0}

rs_chat_message_chat_user                                                        =	user

rs_chat_voice_holddown_talk                                                      =	Hold to Talk

rs_chat_voice_release_cancel                                                     =	Release to Cancel

rs_chat_voice_release_finish                                                     =	Release to Send

rs_chat_voice_slide_cancel                                                       =	Swipe up to Cancel

rs_chat_voice_tooshort                                                           =	Message too short

rs_common_biznetworkissue_tips                                                   =	Failed to connect, please check your network

rs_common_disconnect_tip                                                         =	Disconnected with the user, check your network.

rs_common_message_connectrestore                                                 =	The connection was restored

rs_common_rsnetworkissue_tips                                                    =	Something's wrong with your partner's network. Please try to connect later.

rs_connect_alert_disconnect_active                                               =	User has ended the session

rs_connect_alert_lost_connect                                                    =	No response, try to reconnect later

rs_connect_another                                                               =	A session is in progress under current account. Can't start a new session.

rs_connect_btn                                                                   =	CONNECT

rs_connect_duration                                                              =	Duration

rs_connect_failed_resolution                                                     =	Network error, please try again later

rs_connect_invalid_code                                                          =	Invalid Connection Code or controlled end is offline

rs_connect_max_concurrent                                                        =	You are only allowed to provide remote support for one device each time.

rs_connect_message_disconnect                                                    =	Failed to connect with user, please check network of devices

rs_connect_message_reconnect                                                     =	You have re-connected with system

rs_connect_occupied                                                              =	User is connecting, please try again later

rs_connect_refuse                                                                =	The user has declined your connection request

rs_connect_support_cancel                                                        =	The user has declined your connection request

rs_connect_time_out                                                              =	No response, try to reconnect later

rs_connect_wait_for_confirmation                                                 =	Waiting for confirmation...

rs_connected_client_remark                                                       =	User：{0}

rs_connected_content                                                             =	Confirm to end session?

rs_connected_newcard_arcamera_des                                                =	Enable AR Camera, let your supporter see what you see.

rs_connected_newcard_des                                                         =	Enable Screen Sharing or AR Camera so the technician can have a better understanding of the problem.

rs_connected_newcard_screenshare_des                                             =	Share your screen, let your supporter see the  device display.

rs_connected_newcard_title                                                       =	Connect successfully

rs_connectin_useroffline_tips                                                    =	The user is offline, please ask them to open Remote Support and check network connection.

rs_connection_device_agree                                                       =	is requesting a connection to your device.

rs_control_addon_title                                                           =	Need Remote Control?

rs_controller_not_control_unenrollment                                           =	The organization will not be able to manage this device after unenrollment.

rs_conversaion_call_reject                                                       =	Voice Call declined

rs_conversaion_call_timeout                                                      =	A Voice Call was received, but you did not answer

rs_copy_board_scuuess                                                            =	Copied to Clipboard

rs_copy_invitation_information                                                   =	Copy ID & Connection Password

rs_count_expired                                                                 =	You've reached the maximum connecting device number! Purchase if needed.

rs_custom_connection_password                                                    =	Set Password

rs_daily_update                                                                  =	Daily

rs_decvice_in_voice                                                              =	A call is ongoing on this device

rs_deploy_method                                                                 =	Deployment method:

rs_descrbe_text1                                                                 =	Remote support for clients

rs_descrbe_text2                                                                 =	Please have your clients install and open AirDroid Remote Support and inform you of their 9-digit Connection Code, \r\nEnter Connection Code on the above area to start your connection.

rs_device_detail_title                                                           =	Device info.

rs_device_info_manu                                                              =	Manufacturer

rs_device_info_model                                                             =	Model

rs_device_info_network_status                                                    =	Network

rs_device_info_network_wwan                                                      =	Cellular

rs_device_info_os                                                                =	OS version

rs_device_info_rom_version                                                       =	Android Version

rs_device_info_root                                                              =	Root Status

rs_device_info_root_false                                                        =	Not Rooted

rs_device_info_root_true                                                         =	Rooted

rs_device_info_screen_size                                                       =	Resolution

rs_device_info_storage_size                                                      =	Total Storage

rs_device_info_storage_size_free                                                 =	Available Storage

rs_device_not_enrolled_organization                                              =	This device is not enrolled in your organization

rs_download_tittle                                                               =	Download Remote Support

rs_end_speech                                                                    =	End Call

rs_enroll                                                                        =	Enroll

rs_enroll_successful                                                             =	Enrollment successful

rs_enter_deployment_code_below                                                   =	Enter Deployment Code below

rs_enter_numbers_characters                                                      =	Enter 8 characters including letters and numbers

rs_enter_password_error                                                          =	Invalid password

rs_enter_password_no_longer_accessible                                           =	Enter the password of the Owner's account or the temporary code to unenroll from this organization. The organization will not be able to manage this device after unenrollment.

rs_entercode_tips                                                                =	Enter Connection Code

rs_errortips_nonactivate                                                         =	You haven't activated Remote Support, tap purchase or apply for trial.

rs_exit_running_background                                                       =	Are you sure you want to exit AirDroid Remote Support? This device will not be controlled once exited.

rs_expired_please_enable                                                         =	Accessibility permission for the remote control add-on is no longer available on the controlled device. Please enable it to use Black Screen Mode.

rs_fixed_password                                                                =	Fixed Password

rs_for_windows                                                                   =	AirDroid Remote Support for Windows

rs_free_trail_begin                                                              =	Your {0} Free Trial begins

rs_free_trail_finish                                                             =	Free Trial has expired\r\nPlease purchase to continue using

rs_free_trail_time                                                               =	remaining {0}

rs_free_trail_time_title                                                         =	(Today's FREE Remain <color>{0}</color>)

rs_free_trail_time_title_android                                                 =	(Today's FREE Remain {0})

rs_free_trail_timeout                                                            =	During the Free Trial, each connection can not be over {0} minutes, purchase if needed.

rs_free_trail_today                                                              =	today

rs_free_trial_apply                                                              =	You've got {0} free trial duration, it'll start countdown once you connected.

rs_free_trial_apply_denied                                                       =	Failed

rs_free_trial_apply_ok                                                           =	Apply for Trial

rs_free_trial_apply_success                                                      =	Success

rs_free_trial_denied                                                             =	Failed to get free trial due to you have applied before.

rs_free_trial_denied_not_owner                                                   =	Hi, please contact the business owner to apply for the free trial

rs_free_trial_new_content                                                        =	Now you can enjoy {0} minutes of FREE Remote Support usage EVERY DAY! Through Remote Support, you can help resolve device issues by sharing others' screens, tutorial gestures and real-time voice chat.

rs_free_trial_new_content1                                                       =	Every day you have {0} minutes free usage,

rs_free_trial_new_finish_tip                                                     =	Sorry, today free usage has used up, you can enjoy {0} minutes of FREE Remote Support usage tomorrow. Or you can purchase our subscription to continue.

rs_free_trial_new_immediately                                                    =	Start Now

rs_free_trial_new_title                                                          =	{0} minutes FREE every day!

rs_free_trial_used_up                                                            =	Insufficient quota for Remote Support, please contact your admin.

rs_gesture_content_disable                                                       =	Disabled

rs_gesture_content_enable                                                        =	Enabled

rs_gesture_iosnonsupport_toast                                                   =	This feature is not supported when your partner uses an iOS device

rs_gesture_tip                                                                   =	Tutorial Gesture/Remote Control

rs_gesture_title                                                                 =	Tutorial Gesture

rs_get_device_info                                                               =	Obtaining device info...

rs_go_setting_page                                                               =	Go to Settings?

rs_guide_des1                                                                    =	Your supporter needs to install AirMirror or AirDroid Business depending on your scenario. AirMirror is for personal and non-commercial use, while AirDroid Business is designed for business.

rs_guide_des2                                                                    =	A 9-digit Connection Code will show on your screen. Provide the code to your supporter so he/she can use it to connect with you.

rs_guide_des3                                                                    =	During the session, you can communicate through Voice Call, Screen Sharing, text, and voice messages to discuss the problem.

rs_guide_des4                                                                    =	If your supporter comes from a business, you can even turn on the AR Camera to show your surroundings in real time and get clear virtual instruction.

rs_guide_howtouse_starttouse                                                     =	Back to connection page

rs_guide_howtouse_title                                                          =	How to use?

rs_guide_title1                                                                  =	Supporter’s app

rs_guide_title2                                                                  =	Provide your Connection Code

rs_guide_title3                                                                  =	Multiple communication tools

rs_GuidingGesture_Close_Text                                                     =	Tutorial Gesture turned off

rs_GuidingGesture_Open_Text                                                      =	Tutorial Gesture turned on

rs_hide_password                                                                 =	Hide

rs_how_add_device                                                                =	How to {0} or {1} add a device?

rs_intro_feature_arcamera_des                                                    =	Share the circumstance through the device camera and the technician can place 3D marks on the images to help you out (Only available for business version)

RS_Introduction_of_functions                                                     =	AirDroid Remote Support is created for businesses seeking remote support and remote control solutions for their devices. It allows you to remotely access and control attended and unattended devices, assign different team roles, monitor device status and group devices for better management.

rs_invalidcode_tips                                                              =	Confirm Connection Code is correct

rs_invite_open_screen_share                                                      =	Invite user to start session

rs_login_in_session                                                              =	A Remote Support session is in progress under current account. Can't log in now.

rs_mac_access_permission_settings                                                =	Permission Settings

rs_mac_accessibility_permission                                                  =	Allow control of your mouse and keyboard when connected to this Mac

rs_mac_accessibility_title                                                       =	Accessibility

rs_mac_change_sleep_settings_button                                              =	Change Sleep Settings

rs_mac_change_sleep_settings_prompt                                              =	To make changes, go to Battery or Energy Saver. (On macOS 13, go to Display)

rs_mac_disable_sleep_mode_instruction                                            =	Set this Mac's sleep mode to "Never".

rs_mac_full_disk_access_permission                                               =	Allow file transfers when connected to this Mac

rs_mac_full_disk_access_title                                                    =	Full Disk Access

rs_mac_general_settings                                                          =	General

rs_mac_guest_mode_notice                                                         =	Guest Mode is currently not supported in AirDroid Remote Support.

rs_mac_microphone_permission                                                     =	Allow voice calls when connected to this Mac

rs_mac_microphone_title                                                          =	Microphone

rs_mac_permission_grant_prompt                                                   =	Please grant the following permissions to AirDroid Remote Support for remote access functionality.

rs_mac_required_remark                                                           =	Required

rs_mac_screen_recording_permission                                               =	Allow screen sharing when connected to this Mac

rs_mac_screen_recording_title                                                    =	Screen Recording

rs_mac_security_settings                                                         =	Security

rs_mac_sleep_settings_warning                                                    =	Sleep mode is currently disabled on this Mac. Once the device enters sleep mode, support staff will no longer be able to connect. To prevent this, go to Battery or Energy Saver settings, or click the button below to change sleep settings.

rs_mac_system_permission_warning                                                 =	Some required permissions haven’t been granted, which may limit remote control functionality

rs_maintaince_info                                                               =	Remote Support(iOS) is under maintenance and unavailable for download. Sorry for any inconvenience you've experienced.

rs_make_sure_trust                                                               =	Make sure you trust the person

rs_managed_someone_consent                                                       =	{0} can manage this device and restrict its functions, but remote support will require someone's consent.

rs_manual_update                                                                 =	Manually

rs_message_file_expired                                                          =	File expired, failed to download

rs_message_send_voice_invite                                                     =	You sent a request for Voice Call...

rs_no_screen_share_tip                                                           =	The user hasn't started screen sharing

rs_non_permision                                                                 =	You haven't purchased Remote Support or it has expired. Please purchase if you need the related service.

rs_notification_action                                                           =	Open app

rs_notification_newmessage                                                       =	You've got a message

rs_one_time_password                                                             =	One-time Password

rs_onnection_not_ready                                                           =	Not ready. Please check your network

rs_onnection_ready                                                               =	Ready to connect

rs_organization_for_security_policy                                              =	Enroll this device in your organization via Deployment Code for security policy control and quick remote connection.

rs_owner_nopermission                                                            =	Failed to log in, account has been removed from Remote Support team

rs_partner_rejected_voice_request                                                =	Your partner rejected your voice call.

rs_pc_added_to                                                                   =	{0} is enrolled in

rs_pc_company_limit                                                              =	Enroll in the above organization? After enrolling, your device will be restricted by the {0} of this organization when using AirDroid Remote Support.

rs_pc_connect_language                                                           =	Connecting to device, changing the language will interrupt the connection.

rs_pc_connection_interrupted_network                                             =	Connection interrupted. Please check your network connection.

rs_pc_deployment_failed                                                          =	Enrollment failed

rs_pc_device_name                                                                =	Device Name

rs_pc_disconnect                                                                 =	Are you sure you want to disconnect?

rs_pc_enter_connection_code                                                      =	Enter the code

rs_pc_failed_join_company                                                        =	Invalid Deployment Code

rs_pc_failed_join_company_network                                                =	Failed to enroll in this organization. Please check your network.

rs_pc_id_connect_id                                                              =	Use the following ID and connection password\r\nID: {0}\r\nConnection Password: {1}

rs_pc_language                                                                   =	Language

rs_pc_not_granted_microphone_permissions                                         =	No microphone is detected, or microphone permission isn't granted on your partner's device.

rs_pc_occurred_deployment                                                        =	Failed to obtain the Deployment Code. Please try again later.

rs_pc_reject                                                                     =	Reject ({0}s)

rs_pc_request_failed                                                             =	Request failed. Please try again.

rs_pc_sure_switch_to_unattended                                                  =	Switch to unattended mode? This will allow {0} to remotely control your device at any time. Make sure you trust this organization to protect your privacy.

rs_pc_switch_to                                                                  =	Switch to {0}

rs_pc_unattended_control_1                                                       =	{0} can remotely control your device at any time.

rs_pc_windows_logout                                                             =	Remote Support is in use, confirm log out?

rs_policy_title                                                                  =	Security Policy

rs_purchase_button                                                               =	Purchase Now

rs_refresh password                                                              =	Refresh Password

rs_RemoteControl_Close_Text                                                      =	Remote Control turned off

rs_RemoteControl_disable                                                         =	This organization has disabled the remote control feature.

rs_RemoteControl_Open_Text                                                       =	Remote Control turned on

rs_requesting_chat_with_you                                                      =	is requesting a voice call

rs_rs_pc_mandatory_upgrade                                                       =	Your AirDroid Remote Support version is too old and no longer supported. Please update to the latest version.

rs_rsuser_loseconnect_tip                                                        =	Due to network issues on the other device, the connection has been lost.

rs_screen_share_tittle                                                           =	Screen Sharing

rs_screenshare_scalebutton_tip                                                   =	Actual size

rs_screenshare_screenshot_tip                                                    =	Screenshots

rs_security_reliability                                                          =	AirDroid Remote Support is a powerful, secure, and reliable solution for remote control of attended/unattended devices, enabling fast troubleshooting.

rs_select_assistance                                                             =	Select a Mode for This Device

rs_settings_ar_anchor_switch_des                                                 =	Enhance the accuracy of markers through understanding the principle of identifying

rs_settings_ar_anchor_switch_tilte                                               =	Show anchors

rs_settings_autoplayvoice_title                                                  =	Auto-Play Voice Messages

rs_share_content                                                                 =	Tap the following link to download Remote Support {0}

rs_share_screen_send_success                                                     =	You sent a request for Screen Sharing...

rs_share_text                                                                    =	Hi, my Connection Code for Remote Support is {0} , please use this code to connect with me.

rs_show_password                                                                 =	Show

rs_sign                                                                          =	Markup

rs_sure_unenroll_organization                                                    =	Unenroll from this organization?

rs_switch_to_attend_tip                                                          =	Enter the password of the Owner's account or the temporary code to switch to attended mode, which will require consent to connect to this device.

rs_temporary_password_update                                                     =	Refresh Connection Password

rs_today_password                                                                =	Today's Password

rs_tutorial_drop_arrow_failed_tips                                               =	Tap the third button of the right bottom corner to show reference points, then drop the arrows

rs_tutorial_guide_tip1                                                           =	Now you can create video tutorials

rs_tutorial_guide_tip2                                                           =	Tap here to start recording

rs_tutorial_tip_content                                                          =	Newly released AirDroid Remote Support. Through this app, you can see the screen of a remote device, and solve technical problems using tools such as Voice Call and Tutorial Gesture.

rs_tutorial_title                                                                =	Apply {0} Free Trial Now!

rs_unattached_deploy_code_help_title                                             =	How to obtain the Deployment Code?

rs_unattached_mode                                                               =	Unattended

rs_unattached_switch_dialog_message                                              =	The controller device will not be able to control this device directly when the attended mode is activated.

rs_undeploy_dialog_title                                                         =	Unenroll Device

rs_unenroll_organization                                                         =	Unenroll from Organization

rs_update_after_controlled                                                       =	After each connection

rs_use_id_connect                                                                =	Connect with ID

rs_used_expired                                                                  =	Remote Support service expired, purchase if needed.

rs_user_exited_this_session                                                      =	Your partner has ended the session.

rs_voice_cancel                                                                  =	Voice Call cancelled by user

rs_voice_cancel_btn                                                              =	Cancel

rs_voice_cancel_request                                                          =	Voice Call declined

rs_voice_connected                                                               =	Voice Call enabled

rs_voice_connection_timed_out                                                    =	Voice connection timed out because your partner didn't accept your voice call.

rs_voice_iscalling                                                               =	Voice Call is in session. Try again later

rs_voice_refuse                                                                  =	Voice Call declined by user

rs_voice_request_timedout                                                        =	Call wasn't answered

rs_voicemessage_autosend                                                         =	The recording's too long, so it has been sent

rs_voip_calling                                                                  =	Voice calling

rs_voip_dlg_tip                                                                  =	End the entire support session

rs_voip_wait_for_call                                                            =	Awaiting response

rs_voipcall_duration_tip                                                         =	Duration {0}

rs_voipcall_endduration_tip                                                      =	Voice Call ended, duration {0}

rs_voipcall_hangup_title                                                         =	End Voice Call?

rs_waiting_accept_voice_invitation                                               =	Waiting for your partner to accept the voice call...

rs_waituseconnect_tip                                                            =	Unstable connection, please try to reconnect

rs_your_id                                                                       =	Your ID

safe_mode_has_been_turned_off                                                    =	Black Screen Mode has been deactivated

safe_mode_successfully_turned_on                                                 =	Black Screen Mode has been activated successfully

save_screenshots_videos                                                          =	Save Screenshots/Videos to:

set_fixed_password                                                               =	Set as a fixed password

set_never_sleep_for_remote_access                                                =	Set the computer to never sleep so you can access it remotely at any time.

set_to_sleep_after_{0}{1}_hours_idle                                             =	This computer is set to sleep after {0} {1} {2} {3} of inactivity.

Setting.Button.About                                                             =	About
Setting.Button.Set                                                               =	Settings
Setting.Label.Settings                                                           =	Settings
Setting.Message.RestarApp                                                        =	You've changed language settings. Restart the software to take effect?

signup_pre_ask                                                                   =	Still a new user?

SOS_Window_SendText_Tip                                                          =	Press ↩ send / ⌥↩ New Line

stream_audio_apple_notuse                                                        =	One-Way Audio is not available on iPhone, iPad and iPod devices.

stream_biz_accessibility_delay_tips                                              =	Due to the device limitation, commands are sent to the device after completing a remote control action. This delay may appear as lagging.

stream_biz_dialogue_device_default                                               =	If your remote device is equipped with dedicated hardware, device media sound will be acquired by default. If not, surroundings sound will be acquired.

stream_biz_dialogue_device_des                                                   =	Acquire the device media sound instead of the surrounding sound

stream_biz_dialogue_device_des2                                                  =	If you would like to hear the device media sound, please install the dedicated hardware: {0} on the device. However, the surrounding sound will not be acquired. When the hardware is not installed, the surrounding sound is acquired by default.

stream_biz_dialogue_device_title                                                 =	Device Media Sound

stream_biz_dialogue_surroundings_default                                         =	By default, you are able to hear the device surrounding sound. To acquire device media sound, please go to our website and purchase the dedicated hardware.

stream_biz_dialogue_surroundings_des                                             =	Device microphone will be activated, and you will be able to hear the device surrounding sound

stream_biz_dialogue_surroundings_title                                           =	Surrounding Sound

stream_biz_dialogue_title                                                        =	Sound on

stream_biz_err_not_support_des                                                   =	Failed to acquire remote sound under General Mode, please switch to Dynamic Mode. Dynamic Mode not only acquires remote sound, but can also reduce lag by auto-adjusting the display quality based on the network conditions. To switch modes, please reconnect the device.

stream_biz_err_not_support_title                                                 =	Acquiring ambient sound around the remote device is not supported

stream_biz_err_switch                                                            =	Switch to Dynamic Mode

stream_biz_hardware_name                                                         =	AirDroid Audio Transmitter

stream_biz_icon_hint                                                             =	Acquire ambient sound around the remote device

stream_biz_mic_used_another                                                      =	A Voice Call is undergoing, please end the call first.

stream_biz_noimage_resolution2                                                   =	Due to the network conditions, data transfer speeds are slow. You can \r\n1. Continue to wait \r\n2. Reconnect the device \r\n3. Switch to General Mode

stream_biz_settings_fps_tips                                                     =	When under poor network conditions, a lower FPS may improve motion clarity.

stream_biz_settings_fps_title                                                    =	FPS

stream_biz_sound_close_failed                                                    =	Failed to stop acquiring remote sound

stream_biz_sound_open_failed                                                     =	Failed to acquire remote sound

stream_biz_status_title                                                          =	Sound Status

stream_biz_switch_mode_bizdaemon_toolow                                          =	Failed to initiate Dynamic Mode. The Biz Daemon version on the remote device is lower than required. Please upgrade the Biz Daemon version or switch to General Mode.

stream_biz_switch_mode_tips                                                      =	After switching to Dynamic Mode, you will need to complete the Non-Root setup again. You can switch back to General Mode, Root your device, or grant the Accessibility permission (available only on Android 7.0 or above) to control the device remotely.

stream_biz_switch_mode_tips_phone                                                =	After switching to Dynamic Mode, you will need to complete the Non-Root setup again using the latest version of AirDroid Business for Win/Mac. You can switch back to General Mode, Root your device, or grant the Accessibility permission (available only on Android 7.0 or above) to control the device remotely.

stream_biz_switchto345_resolution                                                =	Please try to reconnect or switch to General mode

stream_biz_voip_close_failed                                                     =	Failed to end Voice Call

stream_biz_voip_open_failed                                                      =	Failed to enable Voice Call

stream_go_nonroot_btn                                                            =	Go Non-Root

stream_sound_closing                                                             =	Stopping acquiring remote sound

stream_sound_opening                                                             =	Acquiring remote sound

stream_voip_closing                                                              =	Ending Voice Call

stream_voip_opening                                                              =	Enabling Voice Call

system_idle_sleep_timer_template                                                 =	This Mac is set to sleep after {0}{1}{2}{3} of inactivity. (The display is currently set to stay on and won’t turn off automatically)

Time_Extension_1h                                                                =	1 hour

time_minute_short_plural                                                         =	{0} min

time_minute_short_singular                                                       =	{0} m

transfer_chatitem_resend_action                                                  =	Resend

Transfer_Nearby_Common_Tips                                                      =	If the device OS you want to connect is different from yours, we recommend your both devices connect to the same Wi-Fi network or sign in with AirDroid account.

Transfer_Nearby_Connecting_DeviceInfo                                            =	{0}'s {1}

Transfer_Nearby_Connecting_Waiting                                               =	Waiting for confirmation...({0})

Transfer_Nearby_ConnectType_Disconnect                                           =	Network disconnect

Transfer_Nearby_ConnectType_WiFi                                                 =	Wi-Fi

Transfer_Nearby_Direct_Content                                                   =	Enable Wi-Fi and bluetooth to discover each other.

Transfer_Nearby_Direct_Title                                                     =	To discover devices:

Transfer_Nearby_Disconnect_Content                                               =	Confirm to disconnect from【{0}】?

Transfer_Nearby_Disconnect_Title                                                 =	Disconnection

Transfer_Nearby_Invite_Content                                                   =	This window won't show again if you select trust this device

Transfer_Nearby_Invite_Title                                                     =	【{0}】requests to connect

Transfer_Nearby_ReceiveFile_Title                                                =	Receive Files

Transfer_Nearby_RemoteConnectMessage_Content                                     =	Your both devices are not connect with the same Wi-Fi and will transfer through remote data. We recommend you connect in the same Wi-Fi with data-free transfer.

Transfer_Nearby_RemoteConnectMessage_Title                                       =	Remote Connecting

Transfer_Nearby_Retransfer_Text1                                                 =	Total {0} files failed

Transfer_Nearby_Retransfer_Text2                                                 =	Resend

Transfer_Nearby_TryDirect_Title                                                  =	Can't discover?

Transfer_Nearby_UnableTransfer_Alert_Cancel                                      =	No, thanks

Transfer_Nearby_UnableTransfer_Alert_Title                                       =	Failed to transfer files

Transfer_Nearby_Unlogin_Alert_Title                                              =	Connect with different Wi-Fi networks

turn_on_AirDroid_Remote_Support_on_boot                                          =	Start AirDroid Remote Support automatically when the computer starts.

two_different_password                                                           =	Passwords do not match.

unenrolling_device                                                               =	Unenrolling device...

uninstall_confirmation_dialog                                                    =	Are you sure you want to uninstall?

Update.Message.GoToWebSite                                                       =	Failed to download the update. You can manually download it from the official website.

Update_ChangeLog_Fail                                                            =	Failed to load. <link>Try again</link>

Update_Fail_Retry_Text                                                           =	Retry

Update_Fail_Text                                                                 =	Failed to download the update. You can try again or manually download it from the official website.

Update_Fail_Website                                                              =	Download from website

Update_Title                                                                     =	Update Available

User_Terms                                                                       =	Terms of Service

verificaition_success_common                                                     =	Verified successfully. You may proceed to use our service.

window_bring_all_to_front                                                        =	Bring All to Front

year_noplan_key                                                                  =	an annual plan

year_plan_key                                                                    =	yearly plan

You_can_manipulation_zoom_in_out_move_screen                                     =	Now you can zoom in on and pan the screen using the two-finger touch

zero_touch_unbind_tip                                                            =	Warning: this action will directly restore the device to its factory settings. If you no longer need to manage this device, please also navigate to the [Devices] tab in Zero Touch portal to remove the device's configuration.
