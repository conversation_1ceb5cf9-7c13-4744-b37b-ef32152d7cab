﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;


#if MAC
using Control = AppKit.NSView;
using Image = AppKit.NSImage;
#elif IOS
using Control = UIKit.UIView;
using Image = UIKit.UIImage;
#else
using System.Windows.Forms;
using System.Drawing;
#endif

namespace iTong.CoreFoundation
{

    #region "--- LabelType ---"

    public enum LabelType
    {
        Anniversary = 0,
        Birthday,
        Mobile,
        iPhone,
        Email,
        HomePage,
        Home,
        Work,
        Main,
        HomeFax,
        WorkFax,
        Pager,
        Other,
        Custom,
        Note,
        None,

        Parent,
        Brother,
        Sister,
        Mother,
        Father,
        Child,
        Friend,
        Spouse,
        Partner,
        Assistant,
        Manager,

        DOMESTIC_PARTNER,
        //同居伴侣
        REFERRED_BY,
        //介绍人
        RELATIVE,
        //亲属

        COMPANY_MAIN,
        CALLBACK,
        CAR,
        ISDN,
        MMS,
        OTHER_FAX,
        RADIO,
        TELEX,
        TTY_TDD,
        WORK_MOBILE,
        WORK_PAGER,

        BLOG,
        FTP,
        PROFILE,

        IM          // 即时信息	
    }

    #endregion

    #region "--- ItemType ---"

    public enum ItemType
    {
        Main,
        Address,
        IM,
        Email,
        PhoneNumber,
        URL,
        Birthday,
        Date,
        Note,
        Related,
        None
    }

    #endregion

    #region "--- IMType ---"

    public enum IMType
    {
        AIM = 0,
        GoogleTalk,
        Yahoo,
        MSN,
        ICQ,
        Jabber,
        Skype,
        QQ,
        Facebook,
        GaduGadu,
        NetMeeting,
        Custom
    }

    #endregion

    #region "--- SortMode ---"

    public enum SortMode
    {
        FirstName,
        LastName,
        Identifier
    }

    #endregion

    #region "--- ChangeType ---"

    public enum ChangeType
    {
        Add,
        Mod,
        Del,
        Backup,
        None
    }

    #endregion

    #region "--- ContentType ---"

    public enum ContentType
    {
        ApplicationSmil,
        AudioAmr,
        ImageJpeg,
        ImagePng,
        TextPlain,
        TextHtml,
        Video3gpp
    }

    #endregion

    #region "--- BaseItem ---"

    public class SubItem
    {

        private string mID = "0";
        ///<summary>
        ///联系人ID
        ///</summary>
        public string ID
        {
            get { return this.mID; }
            set
            {
                if (value != null && value != this.mID)
                {
                    this.mID = value;
                }
            }
        }

        protected string mKey = string.Empty;
        ///<summary>
        ///Item的Key
        ///</summary>
        public virtual string Key
        {
            get { return this.mKey; }
            set
            {
                if (this.mKey != value)
                {
                    this.mKey = value;
                }
            }
        }

        public SubItem()
        {
            this.mKey = NewGuidString();
        }

        public SubItem(string strID)
            : this()
        {

            this.mID = strID;
        }

        public static string NewGuidString()
        {
            return Guid.NewGuid().ToString("D").ToUpper();
        }

        private ChangeType mChangeType = ChangeType.None;
        public ChangeType ChangeType
        {
            get { return this.mChangeType; }
            set { this.mChangeType = value; }
        }
    }

    public class BaseItem : SubItem
    {

        public override string Key
        {
            get { return this.mKey; }
            set
            {
                if (this.mKey != value)
                {
#if IOS
                    if (this.mControl != null && this.mControl.AccessibilityIdentifier.ToString() == this.mKey)
                        this.mControl.AccessibilityIdentifier = value;
#elif MAC
                    if (this.mControl != null && this.mControl.Identifier == this.mKey)
                        this.mControl.Identifier = value;
#else
                    if (this.mControl != null && this.mControl.Name == this.mKey)
                        this.mControl.Name = value;
#endif
                    this.mKey = value;
                }
            }
        }

        private string mLabel = string.Empty;
        ///<summary>
        ///标签
        ///</summary>
        public string Label
        {
            get { return this.mLabel; }
            set { this.mLabel = value; }
        }


        private Control mControl = null;
        public Control Control
        {
            get { return this.mControl; }
            set { this.mControl = value; }
        }

        public static int Compare(string xKey, string yKey)
        {
            int intReturn = 0;
            int intX = xKey.IndexOf("/");
            int intY = yKey.IndexOf("/");

            if (intX == 0 || intY == 0)
            {
                if (intX == intY)
                {
                    intReturn = 0;
                }
                else
                {
                    intReturn = intY - intX;
                }
            }
            else
            {
                int intValueA = 0;
                int intValueB = 0;

                int.TryParse(xKey.Substring(intX + 1), out intValueA);
                int.TryParse(yKey.Substring(intY + 1), out intValueB);

                intReturn = intValueA - intValueB;
            }

            return intReturn;
        }
    }

    public class LabelItem : BaseItem
    {

        private LabelType mLabelType = LabelType.None;
        ///<summary>
        ///标签类型
        ///</summary>
        public LabelType LabelType
        {
            get { return this.mLabelType; }
            set { this.mLabelType = value; }
        }

    }

    public class BackupItem : LabelItem
    {

        private string mKeyBackup = "";
        ///<summary>
        ///备份时唯一标识
        ///</summary>
        public string KeyBackup
        {
            get { return this.mKeyBackup; }
            set
            {
                if (value != null)
                {
                    this.mKeyBackup = value;
                }
            }
        }
    }

    #endregion

    #region "--- ContactGroup ---"

    public class ContactGroup : BaseItem
    {

        private bool mEnabled = true;
        public bool Enabled
        {
            get { return this.mEnabled; }
            set { this.mEnabled = value; }
        }

        private bool mHasLoad = false;
        public bool HasLoad
        {
            get { return this.mHasLoad; }
            set { this.mHasLoad = value; }
        }

        private int mCount = 0;
        public int Count
        {
            get { return this.mCount; }
            set { this.mCount = value; }
        }

        private string mName = "";
        ///<summary>
        ///分组名称
        ///</summary>
        public string Name
        {
            get { return this.mName; }
            set { this.mName = value; }
        }

        private Dictionary<string, SubItem> mMembers = new Dictionary<string, SubItem>();
        ///<summary>
        ///成员列表
        ///</summary>
        public Dictionary<string, SubItem> Members
        {
            get { return this.mMembers; }
            set { this.mMembers = value; }
        }

        private string mAcctName = "";
        ///<summary>
        ///帐号分组类似
        ///</summary>
        public string AcctName
        {
            get { return this.mAcctName; }
            set { this.mAcctName = value; }
        }

        private string mAcctType = "";
        ///<summary>
        ///帐号分组名称
        ///</summary>
        public string AcctType
        {
            get { return this.mAcctType; }
            set { this.mAcctType = value; }
        }

    }

    #endregion

    #region "--- Contact ---"

    public class Contact
    {

        private bool mHasPhoto = false;
        public bool HasPhoto
        {
            get { return this.mHasPhoto; }
            set { this.mHasPhoto = value; }
        }

        private string mIdentifierBackup = "";
        ///<summary>
        ///备份时唯一标识
        ///</summary>
        public string IdentifierBackup
        {
            get { return this.mIdentifierBackup; }
            set
            {
                if (value != null)
                {
                    this.mIdentifierBackup = value;
                }
            }
        }

        private string mIdentifier = "";
        ///<summary>
        ///唯一标识
        ///</summary>
        public string Identifier
        {
            get { return this.mIdentifier; }
            set
            {
                if (value != null && value != this.mIdentifier)
                {
                    if (IdentifierChanged != null)
                    {
                        IdentifierChanged(this, new ContactEventArgs(value, this.mIdentifier));
                    }
                    this.mIdentifier = value;
                }
            }
        }

        private System.DateTime mBirthday = System.DateTime.MinValue;
        ///<summary>
        ///生日
        ///</summary>
        public System.DateTime Birthday
        {
            get { return this.mBirthday; }
            set { this.mBirthday = value; }
        }

        ///<summary>
        ///名字
        ///</summary>
        public string FirstName
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.GivenName;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.GivenName = value;
            }
        }

        ///<summary>
        ///名字的拼音
        ///</summary>
        public string FirstNamePinyin
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.PhoneticGivenName;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.PhoneticGivenName = value;
            }
        }

        ///<summary>
        ///头像
        ///</summary>
        public Image Photo
        {
            get
            {
                if (mPictureItem == null)
                {
                    return null;
                }
                else
                {
                    return this.mPictureItem.Photo;
                }
            }
            set
            {
                if (this.mPictureItem == null)
                {
                    if (value != null)
                    {
                        this.mPictureItem = new PictureItem();
                        this.mPictureItem.Photo = value;
                    }
                }
                else
                {
                    this.mPictureItem.Photo = value;
                }
            }
        }

        private PictureItem mPictureItem = null;
        public PictureItem PictureItem
        {
            get { return this.mPictureItem; }
            set { this.mPictureItem = value; }
        }

        ///<summary>
        ///姓
        ///</summary>
        public string LastName
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.FamilyName;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.FamilyName = value;
            }
        }

        ///<summary>
        ///姓的拼音
        ///</summary>
        public string LastNamePinyin
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.PhoneticFamilyName;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.PhoneticFamilyName = value;
            }
        }

        ///<summary>
        ///中间名
        ///</summary>
        public string MiddleName
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.MiddleName;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.MiddleName = value;
            }
        }

        public string MiddleNamePinyin
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.PhoneticMiddleName;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.PhoneticMiddleName = value;
            }
        }

        ///<summary>
        ///后缀
        ///</summary>
        public string Suffix
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.Suffix;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.Suffix = value;
            }
        }

        ///<summary>
        ///前缀
        ///</summary>
        public string Prefix
        {
            get
            {
                if (this.mNameItem == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mNameItem.Prefix;
                }
            }
            set
            {
                if (this.mNameItem == null)
                {
                    this.mNameItem = new NameItem();
                }
                this.mNameItem.Prefix = value;
            }
        }

        private NameItem mNameItem = null;
        public NameItem NameItem
        {
            get { return this.mNameItem; }
            set { this.mNameItem = value; }
        }

        private string mNickName = string.Empty;
        ///<summary>
        ///昵称
        ///</summary>
        public string NickName
        {
            get
            {
                if (mListNickName.Count > 0)
                {
                    return this.mListNickName[0].Value;
                }
                else
                {
                    return this.mNickName;
                }
            }
            set
            {
                if (this.mListNickName.Count > 0)
                {
                    foreach (ContactItem item in this.mListNickName)
                    {
                        if (item.Value == this.mNickName)
                        {
                            item.Value = value;
                        }
                    }
                }
                else
                {
                    ContactItem item = new ContactItem();
                    item.Value = value;

                    this.mListNickName.Add(item);
                }

                this.mNickName = value;
            }
        }

        private List<ContactItem> mListNickName = new List<ContactItem>();
        public List<ContactItem> NickNameEx
        {
            get { return this.mListNickName; }
            set { this.mListNickName = value; }
        }

        private string mNotes = string.Empty;
        ///<summary>
        ///备忘录
        ///</summary>
        public string Notes
        {
            get
            {
                if (mListNote.Count > 0)
                {
                    return this.mListNote[0].Value;
                }
                else
                {
                    return this.mNotes;
                }
            }
            set
            {
                if (this.mListNote.Count > 0)
                {
                    foreach (ContactItem item in this.mListNote)
                    {
                        if (item.Value == this.mNotes)
                        {
                            item.Value = value;
                        }
                    }
                }
                else
                {
                    ContactItem item = new ContactItem();
                    item.Value = value;

                    this.mListNote.Add(item);
                }

                this.mNotes = value;
            }
        }

        private List<ContactItem> mListNote = new List<ContactItem>();
        public List<ContactItem> NotesEx
        {
            get { return this.mListNote; }
            set { this.mListNote = value; }
        }


        private OrganizationItem mOrganization = null;
        public OrganizationItem Organization
        {
            get { return this.mOrganization; }
            set { this.mOrganization = value; }
        }

        ///<summary>
        ///公司
        ///</summary>
        public string CompanyName
        {
            get
            {
                if (this.mOrganization == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mOrganization.Value;
                }
            }
            set
            {
                if (this.mOrganization == null)
                {
                    this.mOrganization = new OrganizationItem();
                }
                this.mOrganization.Value = value;
            }
        }

        ///<summary>
        ///部门
        ///</summary>
        public string Department
        {
            get
            {
                if (this.mOrganization == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mOrganization.Department;
                }
            }
            set
            {
                if (this.mOrganization == null)
                {
                    this.mOrganization = new OrganizationItem();
                }
                this.mOrganization.Department = value;
            }
        }

        ///<summary>
        ///职务
        ///</summary>
        public string JobTitle
        {
            get
            {
                if (this.mOrganization == null)
                {
                    return string.Empty;
                }
                else
                {
                    return this.mOrganization.Title;
                }
            }
            set
            {
                if (this.mOrganization == null)
                {
                    this.mOrganization = new OrganizationItem();
                }
                this.mOrganization.Title = value;
            }
        }

        public bool ContainPhoneNumber(string phoneNumber)
        {
            bool blnFind = false;

            foreach (ContactItem item in this.mListPhoneNumber)
            {
                if (item.Value.Contains(phoneNumber) || item.ValueFormat.Contains(phoneNumber))
                {
                    blnFind = true;
                    break;
                }
            }

            return blnFind;
        }

        private List<ContactItem> mListPhoneNumber = new List<ContactItem>();
        public List<ContactItem> PhoneNumbers
        {
            get { return this.mListPhoneNumber; }
            set { this.mListPhoneNumber = value; }
        }

        private List<ContactItem> mListEmail = new List<ContactItem>();
        public List<ContactItem> Emails
        {
            get { return this.mListEmail; }
            set { this.mListEmail = value; }
        }

        private List<ContactItem> mListRelated = new List<ContactItem>();
        public List<ContactItem> Relateds
        {
            get { return this.mListRelated; }
            set { this.mListRelated = value; }
        }

        private List<ContactItem> mListURL = new List<ContactItem>();
        public List<ContactItem> URLs
        {
            get { return this.mListURL; }
            set { this.mListURL = value; }
        }

        private List<ContactItem> mListDate = new List<ContactItem>();
        public List<ContactItem> Dates
        {
            get { return this.mListDate; }
            set { this.mListDate = value; }
        }

        private List<AddressItem> mListAddress = new List<AddressItem>();
        public List<AddressItem> Addresses
        {
            get { return this.mListAddress; }
            set { this.mListAddress = value; }
        }

        private List<IMItem> mListIM = new List<IMItem>();
        public List<IMItem> IMs
        {
            get { return this.mListIM; }
            set { this.mListIM = value; }
        }

        private ChangeType mChangeType = ChangeType.None;
        public ChangeType ChangeType
        {
            get { return this.mChangeType; }
            set
            {
                this.mChangeType = value;

                if (this.mChangeType == ChangeType.Del || this.mChangeType == ChangeType.None)
                {
                    if (this.mChangeType == ChangeType.None)
                    {
                        this.RemoveDelItem(this.mListAddress);
                        this.RemoveDelItem(this.mListDate);
                        this.RemoveDelItem(this.mListEmail);
                        this.RemoveDelItem(this.mListIM);
                        this.RemoveDelItem(this.mListPhoneNumber);
                        this.RemoveDelItem(this.mListURL);
                    }
                    this.SetItemChangeType(this.mListAddress, value);
                    this.SetItemChangeType(this.mListDate, value);
                    this.SetItemChangeType(this.mListEmail, value);
                    this.SetItemChangeType(this.mListIM, value);
                    this.SetItemChangeType(this.mListPhoneNumber, value);
                    this.SetItemChangeType(this.mListURL, value);
                }
            }
        }

        public event EventHandler<ContactEventArgs> IdentifierChanged;

        public Contact()
        {
            this.mIdentifier = BaseItem.NewGuidString();
        }


        private void RemoveDelItem(object list)
        {
            List<BaseItem> delList = new List<BaseItem>();
            foreach (BaseItem item in (IEnumerable)list)
            {
                if (item.ChangeType == ChangeType.Del)
                {
                    delList.Add(item);
                }
            }

            foreach (BaseItem item in delList)
            {
                ((IList)list).Remove(item);
            }
        }

        private void SetItemChangeType(object list, ChangeType type)
        {
            foreach (BaseItem item in (IEnumerable)list)
            {
                item.ChangeType = type;
            }
        }

        private bool _IsFindDate;

        public bool IsFindDate
        {
            get { return _IsFindDate; }
            set { _IsFindDate = value; }
        }

    }

    #endregion

    #region "--- AddressItem ---"

    public class AddressItem : LabelItem
    {

        private string mCity = string.Empty;
        ///<summary>
        ///城市
        ///</summary>
        public string City
        {
            get { return this.mCity; }
            set { this.mCity = value; }
        }

        private string mCountry = string.Empty;
        ///<summary>
        ///国家
        ///</summary>
        public string Country
        {
            get { return this.mCountry; }
            set { this.mCountry = value; }
        }

        private string mCountryCode = string.Empty;
        ///<summary>
        ///国家代码
        ///</summary>
        public string CountryCode
        {
            get { return this.mCountryCode; }
            set { this.mCountryCode = value; }
        }

        private string mPostalCode = string.Empty;
        ///<summary>
        ///邮编
        ///</summary>
        public string PostalCode
        {
            get { return this.mPostalCode; }
            set { this.mPostalCode = value; }
        }

        private string mProvince = string.Empty;
        ///<summary>
        ///省份
        ///</summary>
        public string Province
        {
            get { return this.mProvince; }
            set { this.mProvince = value; }
        }

        private string mStreet = string.Empty;
        ///<summary>
        ///街道
        ///</summary>
        public string Street
        {
            get { return this.mStreet; }
            set { this.mStreet = value; }
        }

        private string mPostBox = string.Empty;
        ///<summary>
        ///Post Office Box number
        ///</summary>
        public string PostBox
        {
            get { return this.mPostBox; }
            set { this.mPostBox = value; }
        }

        private string mNeighborhood = string.Empty;
        ///<summary>
        ///临近街坊
        ///</summary>
        public string Neighborhood
        {
            get { return this.mNeighborhood; }
            set { this.mNeighborhood = value; }
        }

        private string mRegion = string.Empty;
        ///<summary>
        ///区域
        ///</summary>
        public string Region
        {
            get { return this.mRegion; }
            set { this.mRegion = value; }
        }

        private bool mIsSimple = false;
        ///<summary>
        ///是简单合并书写地址
        ///</summary>
        public bool IsSimple
        {
            get { return this.mIsSimple; }
            set { this.mIsSimple = value; }
        }

        private string mAddress = string.Empty;
        ///<summary>
        ///格式化地址
        ///</summary>
        public string Address
        {
            get { return this.mAddress; }
            set { this.mAddress = value; }
        }

    }

    #endregion

    #region "--- IMItem ---"

    public class IMItem : ContactItem
    {

        private IMType mIMType = IMType.AIM;
        ///<summary>
        ///IM类型
        ///</summary>
        public IMType IMType
        {
            get { return this.mIMType; }
            set { this.mIMType = value; }
        }

        private string mIIMCustom = string.Empty;
        public string IMCustom
        {
            get { return this.mIIMCustom; }
            set { this.mIIMCustom = value; }
        }

    }

    #endregion

    #region "--- OrganizationItem ---"

    public class OrganizationItem : ContactItem
    {

        private string mTitle = string.Empty;
        ///<summary>
        ///职称
        ///</summary>
        public string Title
        {
            get { return this.mTitle; }
            set { this.mTitle = value; }
        }

        private string mDepartment = string.Empty;
        ///<summary>
        ///部门
        ///</summary>
        public string Department
        {
            get { return this.mDepartment; }
            set { this.mDepartment = value; }
        }

        private string mJobDescription = string.Empty;
        ///<summary>
        ///工作描述
        ///</summary>
        public string JobDescription
        {
            get { return this.mJobDescription; }
            set { this.mJobDescription = value; }
        }

        private string mSymbol = string.Empty;
        ///<summary>
        ///象征
        ///</summary>
        public string Symbol
        {
            get { return this.mSymbol; }
            set { this.mSymbol = value; }
        }

        private string mPhoneticName = string.Empty;
        ///<summary>
        ///拼音名称
        ///</summary>
        public string PhoneticName
        {
            get { return this.mPhoneticName; }
            set { this.mPhoneticName = value; }
        }

        private string mOfficeLocation = string.Empty;
        ///<summary>
        ///办公地点
        ///</summary>
        public string OfficeLocation
        {
            get { return this.mOfficeLocation; }
            set { this.mOfficeLocation = value; }
        }

        private string mPhoneticNameStyle = string.Empty;
        ///<summary>
        ///拼音名称类型
        ///</summary>
        public string PhoneticNameStyle
        {
            get { return this.mPhoneticNameStyle; }
            set { this.mPhoneticNameStyle = value; }
        }

    }

    #endregion

    #region "--- NameItem ---"

    public class NameItem : LabelItem
    {

        private string mDisplayName = string.Empty;
        public string DisplayName
        {
            get { return this.mDisplayName; }
            set { this.mDisplayName = value; }
        }

        private string mGivenName = string.Empty;
        public string GivenName
        {
            get { return this.mGivenName; }
            set { this.mGivenName = value; }
        }

        private string mFamilyName = string.Empty;
        public string FamilyName
        {
            get { return this.mFamilyName; }
            set { this.mFamilyName = value; }
        }

        private string mPrefix = string.Empty;
        public string Prefix
        {
            get { return this.mPrefix; }
            set { this.mPrefix = value; }
        }

        private string mMiddleName = string.Empty;
        public string MiddleName
        {
            get { return this.mMiddleName; }
            set { this.mMiddleName = value; }
        }

        private string mSuffix = string.Empty;
        public string Suffix
        {
            get { return this.mSuffix; }
            set { this.mSuffix = value; }
        }

        private string mPhoneticGivenName = string.Empty;
        public string PhoneticGivenName
        {
            get { return this.mPhoneticGivenName; }
            set { this.mPhoneticGivenName = value; }
        }

        private string mPhoneticMiddleName = string.Empty;
        public string PhoneticMiddleName
        {
            get { return this.mPhoneticMiddleName; }
            set { this.mPhoneticMiddleName = value; }
        }

        private string mPhoneticFamilyName = string.Empty;
        public string PhoneticFamilyName
        {
            get { return this.mPhoneticFamilyName; }
            set { this.mPhoneticFamilyName = value; }
        }

    }

    #endregion

    #region "--- PictureItem ---"

    public class PictureItem : SubItem
    {

        private Image mPhoto = null;
        ///<summary>
        ///头像
        ///</summary>
        public Image Photo
        {
            get { return this.mPhoto; }
            set
            {
                if (this.mPhoto != null && !this.mPhoto.Equals(value))
                {
                    this.mPhoto.Dispose();
                    this.mPhoto = null;
                }

                this.mPhoto = value;
                this.ChangeType = ChangeType.Mod;
            }
        }

        public void Dispose()
        {
            try
            {
                if (this.mPhoto != null)
                {
                    this.mPhoto.Dispose();
                    this.mPhoto = null;
                }

                GC.SuppressFinalize(this);
            }
            catch (Exception ex)
            {
            }
        }

    }

    #endregion

    #region "--- ContactItem ---"

    public class ContactItem : LabelItem
    {

        private string mValue = string.Empty;
        ///<summary>
        ///值
        ///</summary>
        public string Value
        {
            get { return this.mValue; }
            set { this.mValue = value; }
        }

        private string mValueFormat = string.Empty;
        ///<summary>
        ///格式化后的值
        ///</summary>
        public string ValueFormat 
        {
            get { return this.mValueFormat; }
            set { this.mValueFormat = value; }
        }
    }

    #endregion

    #region "--- ContactEventArgs ---"

    public class ContactEventArgs : EventArgs
    {

        private string mNewID = string.Empty;
        public string NewID
        {
            get { return this.mNewID; }
            set { this.mNewID = value; }
        }

        private string mOldID = string.Empty;
        public string OldID
        {
            get { return this.mOldID; }
            set { this.mOldID = value; }
        }


        public ContactEventArgs()
        {
        }

        public ContactEventArgs(string newID, string oldID)
        {
            this.mNewID = newID;
            this.mOldID = oldID;
        }

    }

    #endregion

    #region "--- SyncConflictEventHandler ---"

    public delegate void StartSaveDBHandler(List<Contact> listContatct);
    public delegate void SyncConflictEventHandler(SyncConflictEventArgs e);

    public class SyncConflictEventArgs : EventArgs
    {
        private object mSrcData;
        /// <summary> 
        /// 源数据 
        /// </summary> 
        public object SrcData
        {
            get { return mSrcData; }
            set { mSrcData = value; }
        }

        private object mDesData;
        /// <summary> 
        /// 目标数据 
        /// </summary> 
        public object DesData
        {
            get { return mDesData; }
            set { mDesData = value; }
        }

        private SelectType m_SelectType = SelectType.None;
        /// <summary> 
        /// 冲突处理方式 
        /// </summary> 
        public SelectType SelectType
        {
            get { return m_SelectType; }
            set { m_SelectType = value; }
        }

        private bool mUnPrompt = false;
        /// <summary> 
        /// 不再引发事件 
        /// </summary> 
        public bool UnPrompt
        {
            get { return mUnPrompt; }
            set { mUnPrompt = value; }
        }
    }

    public enum SelectType
    {
        /// <summary> 
        /// 跳过 
        /// </summary> 
        Skip,
        /// <summary> 
        /// 新增 
        /// </summary> 
        CreateNew,
        /// <summary> 
        /// 覆盖 
        /// </summary> 
        Cover,
        /// <summary> 
        /// 合并 
        /// </summary> 
        Merge,
        /// <summary> 
        /// 取消 
        /// </summary> 
        Cancel,
        /// <summary> 
        /// 不操作 
        /// </summary> 
        None
    }

    #endregion

    #region "--- Comparer ---"

    public class ContactComparer : System.Collections.Generic.IComparer<Contact>
    {

        private SortMode mSort = SortMode.LastName;
        public SortMode SortMode
        {
            get { return mSort; }
            set { mSort = value; }
        }

        public ContactComparer(SortMode mode)
        {
            this.mSort = mode;
        }

        public int Compare(Contact objX, Contact objY)
        {
            if (this.SortMode == SortMode.Identifier)
            {
                int intX = Convert.ToInt32(objX.Identifier);
                int intY = Convert.ToInt32(objY.Identifier);

                return intX - intY;

            }
            else
            {
                string strNameX = "";
                string strNameY = "";
                if (this.SortMode == SortMode.LastName)
                {
                    strNameX = objX.LastName + " " + objX.FirstName;
                    strNameY = objY.LastName + " " + objY.FirstName;

                }
                else if (this.SortMode == SortMode.FirstName)
                {
                    strNameX = objX.FirstName + " " + objX.LastName;
                    strNameY = objY.FirstName + " " + objY.LastName;

                }
                strNameX = strNameX.TrimStart();
                strNameY = strNameY.TrimStart();

                strNameX = PinYinClass.MakePinYin(strNameX, PinYinOptions.Default);
                strNameY = PinYinClass.MakePinYin(strNameY, PinYinOptions.Default);

                return string.Compare(strNameX, strNameY);
            }
        }

    }

    public class AddressItemComparer : System.Collections.Generic.IComparer<AddressItem>
    {

        public int Compare(AddressItem x, AddressItem y)
        {
            return BaseItem.Compare(x.Key, y.Key);
        }

    }

    public class IMItemComparer : System.Collections.Generic.IComparer<IMItem>
    {

        public int Compare(IMItem x, IMItem y)
        {
            return BaseItem.Compare(x.Key, y.Key);
        }

    }

    public class ContactItemComparer : System.Collections.Generic.IComparer<ContactItem>
    {

        public int Compare(ContactItem x, ContactItem y)
        {
            return BaseItem.Compare(x.Key, y.Key);
        }

    }

    public class ContactGroupComparer : System.Collections.Generic.IComparer<ContactGroup>
    {

        public int Compare(ContactGroup x, ContactGroup y)
        {
            return BaseItem.Compare(x.Key, y.Key);
        }

    }

    #endregion

    public interface IDeviceContact
    {
        List<string> CustomLabels { get; set; }
        List<ContactGroup> Groups { get; set; }
        List<Contact> Contacts { get; set; }
        Dictionary<LabelType, string> SystemLabels { get; set; }

        SortMode SortMode { get; set; }
        Contact GetContact(string identifier);
        ContactGroup GetGroup(string key);
        ContactGroup GetGroupByName(string name);


        bool Load();
        bool Changes(List<Contact> listContact, List<ContactGroup> listGroup, SyncConflictEventHandler exitCallBack);

        bool Changes(Contact contact);
        bool Changes(ContactGroup @group);
        bool Changes(List<Contact> listContact);
        bool Changes(List<ContactGroup> listGroup);
        bool Changes(Contact contact, ContactGroup @group);
        bool Changes(Contact contact, List<ContactGroup> listGroup);
        bool Changes(List<Contact> listContact, ContactGroup @group);
        bool Changes(List<Contact> listContact, List<ContactGroup> listGroup);
    }
}
