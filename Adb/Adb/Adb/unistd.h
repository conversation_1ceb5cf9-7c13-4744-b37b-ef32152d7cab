/** This file is part of the Mingw32 package. * unistd.h maps (roughly) to io.h */
#ifndef _UNISTD_H 
#define _UNISTD_H 
#include <io.h>
#include "rsa.h"
#include <basetsd.h>
#include <process.h> 
#endif 
/* _UNISTD_H */ 

#define S_ISREG(mode) ((mode&S_IFREG)==S_IFREG)

#define PATH_MAX MAX_PATH

#define F_OK	0
#define W_OK	2
#define R_OK	4
#define X_OK	1

typedef int pid_t;

typedef unsigned char       uint8_t;
typedef unsigned short      uint16_t;
typedef unsigned int        uint32_t;
typedef unsigned __int64    uint64_t;