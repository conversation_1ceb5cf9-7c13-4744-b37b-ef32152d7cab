/*
 * Copyright (C) 2009 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "usb_vendors.h"

#include <stdio.h>

#ifdef _WIN32
#  define WIN32_LEAN_AND_MEAN
#  include "windows.h"
#  include "shlobj.h"
#  include "stdafx.h"

#else
#  include "unistd.h"
#  include <sys/stat.h>
#endif

#include "sysdeps.h"
#include "adb.h"

#define ANDROID_PATH            ".android"
#define ANDROID_ADB_INI         "adb_usb.ini"

#define TRACE_TAG               TRACE_USB

// Google's USB Vendor ID
#define VENDOR_ID_GOOGLE        0x18d1
// Intel's USB Vendor ID
#define VENDOR_ID_INTEL         0x8087
// HTC's USB Vendor ID
#define VENDOR_ID_HTC           0x0bb4
// Samsung's USB Vendor ID
#define VENDOR_ID_SAMSUNG       0x04e8
// Motorola's USB Vendor ID
#define VENDOR_ID_MOTOROLA      0x22b8
// LG's USB Vendor ID
#define VENDOR_ID_LGE           0x1004
// Huawei's USB Vendor ID
#define VENDOR_ID_HUAWEI        0x12D1
// Acer's USB Vendor ID
#define VENDOR_ID_ACER          0x0502
// Sony Ericsson's USB Vendor ID
#define VENDOR_ID_SONY_ERICSSON 0x0FCE
// Foxconn's USB Vendor ID
#define VENDOR_ID_FOXCONN       0x0489
// Dell's USB Vendor ID
#define VENDOR_ID_DELL          0x413c
// Nvidia's USB Vendor ID
#define VENDOR_ID_NVIDIA        0x0955
// Garmin-Asus's USB Vendor ID
#define VENDOR_ID_GARMIN_ASUS   0x091E
// Sharp's USB Vendor ID
#define VENDOR_ID_SHARP         0x04dd
// ZTE's USB Vendor ID
#define VENDOR_ID_ZTE           0x19D2
// Kyocera's USB Vendor ID
#define VENDOR_ID_KYOCERA       0x0482
// Pantech's USB Vendor ID
#define VENDOR_ID_PANTECH       0x10A9
// Qualcomm's USB Vendor ID
#define VENDOR_ID_QUALCOMM      0x05c6
// On-The-Go-Video's USB Vendor ID
#define VENDOR_ID_OTGV          0x2257
// NEC's USB Vendor ID
#define VENDOR_ID_NEC           0x0409
// Panasonic Mobile Communication's USB Vendor ID
#define VENDOR_ID_PMC           0x04DA
// Toshiba's USB Vendor ID
#define VENDOR_ID_TOSHIBA       0x0930
// SK Telesys's USB Vendor ID
#define VENDOR_ID_SK_TELESYS    0x1F53
// KT Tech's USB Vendor ID
#define VENDOR_ID_KT_TECH       0x2116
// Asus's USB Vendor ID
#define VENDOR_ID_ASUS          0x0b05
// Philips's USB Vendor ID
#define VENDOR_ID_PHILIPS       0x0471
// Texas Instruments's USB Vendor ID
#define VENDOR_ID_TI            0x0451
// Funai's USB Vendor ID
#define VENDOR_ID_FUNAI         0x0F1C
// Gigabyte's USB Vendor ID
#define VENDOR_ID_GIGABYTE      0x0414
// IRiver's USB Vendor ID
#define VENDOR_ID_IRIVER        0x2420
// Compal's USB Vendor ID
#define VENDOR_ID_COMPAL        0x1219
// T & A Mobile Phones' USB Vendor ID
#define VENDOR_ID_T_AND_A       0x1BBB
// LenovoMobile's USB Vendor ID
#define VENDOR_ID_LENOVOMOBILE  0x2006
// Lenovo's USB Vendor ID
#define VENDOR_ID_LENOVO        0x17EF
// Vizio's USB Vendor ID
#define VENDOR_ID_VIZIO         0xE040
// K-Touch's USB Vendor ID
#define VENDOR_ID_K_TOUCH       0x24E3
// Pegatron's USB Vendor ID
#define VENDOR_ID_PEGATRON      0x1D4D
// Archos's USB Vendor ID
#define VENDOR_ID_ARCHOS        0x0E79
// Positivo's USB Vendor ID
#define VENDOR_ID_POSITIVO      0x1662
// Fujitsu's USB Vendor ID
#define VENDOR_ID_FUJITSU       0x04C5
// Lumigon's USB Vendor ID
#define VENDOR_ID_LUMIGON       0x25E3
// Quanta's USB Vendor ID
#define VENDOR_ID_QUANTA        0x0408
// INQ Mobile's USB Vendor ID
#define VENDOR_ID_INQ_MOBILE    0x2314
// Sony's USB Vendor ID
#define VENDOR_ID_SONY          0x054C
// Lab126's USB Vendor ID
#define VENDOR_ID_LAB126        0x1949
// Yulong Coolpad's USB Vendor ID
#define VENDOR_ID_YULONG_COOLPAD 0x1EBF
// Kobo's USB Vendor ID
#define VENDOR_ID_KOBO          0x2237
// Teleepoch's USB Vendor ID
#define VENDOR_ID_TELEEPOCH     0x2340
// AnyDATA's USB Vendor ID
#define VENDOR_ID_ANYDATA       0x16D5
// Harris's USB Vendor ID
#define VENDOR_ID_HARRIS        0x19A5
// OPPO's USB Vendor ID
#define VENDOR_ID_OPPO          0x22D9
// Xiaomi's USB Vendor ID
#define VENDOR_ID_XIAOMI        0x2717
// BYD's USB Vendor ID
#define VENDOR_ID_BYD           0x19D1
// OUYA's USB Vendor ID
#define VENDOR_ID_OUYA          0x2836
// Haier's USB Vendor ID
#define VENDOR_ID_HAIER         0x201E
// Hisense's USB Vendor ID
#define VENDOR_ID_HISENSE       0x109b
// MTK's USB Vendor ID
#define VENDOR_ID_MTK           0x0e8d
// B&N Nook's USB Vendor ID
#define VENDOR_ID_NOOK          0x2080
// Qisda's USB Vendor ID
#define VENDOR_ID_QISDA         0x1D45
// ECS's USB Vendor ID
#define VENDOR_ID_ECS           0x03fc
// MSI's USB Vendor ID
#define VENDOR_ID_MSI           0x0DB0
// Wacom's USB Vendor ID
#define VENDOR_ID_WACOM         0x0531
// MicroMax's USB Vendor ID
#define	VENDOR_ID_MICROMAX   	0x1d91
// Kodak's USB Vendor ID
#define VENDOR_ID_KODAK			0x040a
// Altek's USB Vendor ID
#define VENDOR_ID_ALTEK			0x143c
// ShangHai BaseCom's USB Vendor ID
#define VENDOR_ID_BASECOM       0x1e0e
// ShangHai Longcheer
#define VENDOR_ID_LONGCHEER     0x1C9E
// Atmel Co.
#define VENDOR_ID_ATMEL			0x03EB
// Mitsumi
#define VENDOR_ID_MITSUMI		0x03EE
// Hewlett Packard
#define VENDOR_ID_HEWLETT		0x03F0
// Amoi: VID_1614&PID_2008
#define VENDOR_ID_AMOI			0x1614
// Diebold, Inc.
#define VENDOR_ID_DIEBOLD		0x03F4
#define VENDOR_ID_YUXIN			0x276A
// Xilinx Inc.
#define VENDOR_ID_XILINX		0x03FD
// ALi Corporation
#define VENDOR_ID_ALI			0x0402
// Future Technology Devices International Limited
#define VENDOR_ID_FUTURE		0x0403
// Weltrend Semiconductor
#define VENDOR_ID_WELTREND		0x040B
// VIA Technologies, Inc.
#define VENDOR_ID_VIA			0x040D
// MCCI
#define VENDOR_ID_MCCI			0x040E
// BUFFALO INC.
#define VENDOR_ID_BUFFALO		0x0411
// Nuvoton Technology Co.
#define VENDOR_ID_NUVOTON		0x0416
// Creative Labs
#define VENDOR_ID_CREATIVE		0x041e
// Nokia Corporation
#define VENDOR_ID_NOKIA			0x0421
// SMSC
#define VENDOR_ID_SMSC			0x0424
// Integrated Device Technology
#define VENDOR_ID_INTEGRATED	0x0426
// Molex Inc.
#define VENDOR_ID_MOLEX			0x042f
// Unisys Corp.
#define VENDOR_ID_UNISYS		0x0432
// Advanced Micro Devices
#define VENDOR_ID_ADVANCED_MICRO	0x0438
// Lexmark International Inc.
#define VENDOR_ID_LEXMARK		0x043d
// EIZO NANAO CORPORATION
#define VENDOR_ID_EIZO_NANAO	0x0440
// Alps Electric Co., Ltd.
#define VENDOR_ID_ALPS			0x044e
// Silicon Integrated Systems Corp.
#define VENDOR_ID_SILICON		0x0457
// KYE Systems Corp.
#define VENDOR_ID_KYE			0x0458
// Renesas Technology Corp.
#define VENDOR_ID_RENESAS		0x045b
// Microsoft Corporation
#define VENDOR_ID_MICROSOFT		0x045e
// Primax Electronics
#define VENDOR_ID_PRIMAX		0x0461
// EATON
#define VENDOR_ID_EATON			0x0463
// Wieson Technologies Co., Ltd.
#define VENDOR_ID_WIESON		0x0468
// American Megatrends
#define VENDOR_ID_MEGATRENDS	0x046b
// Logitech Inc.
#define VENDOR_ID_LOGITECH		0x046d
// Behavior Tech Computer Corporation
#define VENDOR_ID_BEHAVIOR		0x046e
// Sanyo Electric Co. Ltd.
#define VENDOR_ID_SANYO			0x0474
// Semtech Corporation
#define VENDOR_ID_SEMTECH		0x047a
// Kensington
#define VENDOR_ID_KENSINGTON	0x047d
// LSI Corporation
#define VENDOR_ID_LSI_CORP		0x047e
// Plantronics, Inc.
#define VENDOR_ID_PLANTRONICS	0x047f
//STMicro electronics
#define VENDOR_ID_STMICRO		0x0483
// ITE Tech Inc.
#define VENDOR_ID_ITETECH		0x048d
// Yamaha Corporation
#define VENDOR_ID_YAMAHA		0x0499
// Hitachi, Ltd.
#define VENDOR_ID_HITACHI		0x04a4
// Visioneer
#define VENDOR_ID_VISIONEER		0x04a7
// Canon Inc.
#define VENDOR_ID_CANON			0x04a9
// Skyworth
#define VENDOR_ID_SKYWORTH		0x05e3
// Nikon Corporation
#define VENDOR_ID_NIKON			0x04b0
// Cypress Semiconductor
#define VENDOR_ID_CYPRESS		0x04b4
// ROHM Co., Ltd.
#define VENDOR_ID_ROHM			0x04b5
// Compal Electronics, Inc.
#define VENDOR_ID_COMPAL_ELEC	0x04b7
// Seiko Epson Corp.
#define VENDOR_ID_SEIKO_EPSON	0x04b8
// I-O Data Device, Inc.
#define VENDOR_ID_IODATA_DEVICE	0x04bb
// FUJIFILM Corporation
#define VENDOR_ID_FUJIFILM		0x04cb
// ST-Ericsson
#define VENDOR_ID_ST_ERICSSON	0x04cc
// Mentor Graphics
#define VENDOR_ID_MENTOR		0x04d6
// Microchip Technology Inc.
#define VENDOR_ID_MICROCHIP		0x04d8
// Holtek Semiconductor, Inc.
#define VENDOR_ID_HOLTEK		0x04d9
// Panasonic
#define VENDOR_ID_PANASONIC		0x04da
// Huan Hsin Holdings Ltd.
#define VENDOR_ID_HUANHSIN		0x04dc
// Exar Corporation
#define VENDOR_ID_EXAR			0x04e2
// SCM Microsystems
#define VENDOR_ID_SCM_MICRO		0x04e6
// Northstar Systems Corp.
#define VENDOR_ID_NORTHSTAR		0x04eb
// Tokyo Electron Device Limited
#define VENDOR_ID_TOKYO			0x04ec
// Victor Company of Japan, Limited
#define VENDOR_ID_VICTOR		0x04f1
// Chicony Electronics Co., Ltd.
#define VENDOR_ID_CHICONY		0x04f2
// Newnex Technology Corp.
#define VENDOR_ID_NEWNEX		0x04f7
//
#define VENDOR_ID_AIKESHI		0x2207
// SHENZHEN JASON ELECTRONICS CO., LTD.
#define VENDOR_ID_JASON			0x21b5
// Dongguan Teconn Electronics Technology Co., Ltd.
#define VENDOR_ID_TECONN		0x21b3
// HUIZHOU HUANGJI PRECISIONS FLEX ELECTRONICAL CO., LTD.
#define VENDOR_ID_HUANGJI		0x2173
// Zhejiang Fousine Science & Technology Co., Ltd.
#define VENDOR_ID_FOUSINE		0x2167
// Ningbo Broad Telecommunication Co., Ltd.
#define VENDOR_ID_NINGBO_BROAD	0x1931
// Shenzhen Xianhe Technology Co., Ltd.
#define VENDOR_ID_XIANHE		0x1930

/** built-in vendor list */
int builtInVendorIds[] = {
    VENDOR_ID_GOOGLE,
    VENDOR_ID_INTEL,
    VENDOR_ID_HTC,
    VENDOR_ID_SAMSUNG,
    VENDOR_ID_MOTOROLA,
    VENDOR_ID_LGE,
    VENDOR_ID_HUAWEI,
    VENDOR_ID_ACER,
    VENDOR_ID_SONY_ERICSSON,
    VENDOR_ID_FOXCONN,
    VENDOR_ID_DELL,
    VENDOR_ID_NVIDIA,
    VENDOR_ID_GARMIN_ASUS,
    VENDOR_ID_SHARP,
    VENDOR_ID_ZTE,
    VENDOR_ID_KYOCERA,
    VENDOR_ID_PANTECH,
    VENDOR_ID_QUALCOMM,
    VENDOR_ID_OTGV,
    VENDOR_ID_NEC,
    VENDOR_ID_PMC,
    VENDOR_ID_TOSHIBA,
    VENDOR_ID_SK_TELESYS,
    VENDOR_ID_KT_TECH,
    VENDOR_ID_ASUS,
    VENDOR_ID_PHILIPS,
    VENDOR_ID_TI,
    VENDOR_ID_FUNAI,
    VENDOR_ID_GIGABYTE,
    VENDOR_ID_IRIVER,
    VENDOR_ID_COMPAL,
    VENDOR_ID_T_AND_A,
    VENDOR_ID_LENOVOMOBILE,
    VENDOR_ID_LENOVO,
    VENDOR_ID_VIZIO,
    VENDOR_ID_K_TOUCH,
    VENDOR_ID_PEGATRON,
    VENDOR_ID_ARCHOS,
    VENDOR_ID_POSITIVO,
    VENDOR_ID_FUJITSU,
    VENDOR_ID_LUMIGON,
    VENDOR_ID_QUANTA,
    VENDOR_ID_INQ_MOBILE,
    VENDOR_ID_SONY,
    VENDOR_ID_LAB126,
    VENDOR_ID_YULONG_COOLPAD,
    VENDOR_ID_KOBO,
    VENDOR_ID_TELEEPOCH,
    VENDOR_ID_ANYDATA,
    VENDOR_ID_HARRIS,
    VENDOR_ID_OPPO,
    VENDOR_ID_XIAOMI,
    VENDOR_ID_BYD,
    VENDOR_ID_OUYA,
    VENDOR_ID_HAIER,
    VENDOR_ID_HISENSE,
    VENDOR_ID_MTK,
    VENDOR_ID_NOOK,
    VENDOR_ID_QISDA,
    VENDOR_ID_ECS,
    VENDOR_ID_MSI,
    VENDOR_ID_WACOM,
	VENDOR_ID_MICROMAX,
	VENDOR_ID_KODAK,
	VENDOR_ID_ALTEK,
	VENDOR_ID_BASECOM,
	VENDOR_ID_LONGCHEER,
	VENDOR_ID_ATMEL,
	VENDOR_ID_MITSUMI,
	VENDOR_ID_HEWLETT,
	VENDOR_ID_AMOI,
	VENDOR_ID_DIEBOLD,
	VENDOR_ID_YUXIN,
	VENDOR_ID_XILINX,
	VENDOR_ID_ALI,
	VENDOR_ID_FUTURE,
	VENDOR_ID_WELTREND,
	VENDOR_ID_VIA,
	VENDOR_ID_MCCI,
	VENDOR_ID_BUFFALO,
	VENDOR_ID_NUVOTON,
	VENDOR_ID_CREATIVE,
	VENDOR_ID_NOKIA,
	VENDOR_ID_SMSC,
	VENDOR_ID_INTEGRATED,
	VENDOR_ID_MOLEX,
	VENDOR_ID_UNISYS,
	VENDOR_ID_ADVANCED_MICRO,
	VENDOR_ID_LEXMARK,
	VENDOR_ID_EIZO_NANAO,
	VENDOR_ID_ALPS, 
	VENDOR_ID_SILICON,
	VENDOR_ID_KYE,
	VENDOR_ID_RENESAS,
	VENDOR_ID_MICROSOFT,
	VENDOR_ID_PRIMAX,
	VENDOR_ID_EATON,
	VENDOR_ID_WIESON,
	VENDOR_ID_MEGATRENDS,
	VENDOR_ID_LOGITECH,
	VENDOR_ID_BEHAVIOR,
	VENDOR_ID_SANYO,
	VENDOR_ID_SEMTECH,
	VENDOR_ID_KENSINGTON,
	VENDOR_ID_LSI_CORP,
	VENDOR_ID_PLANTRONICS,
	VENDOR_ID_STMICRO,
	VENDOR_ID_ITETECH,
	VENDOR_ID_YAMAHA,
	VENDOR_ID_HITACHI,
	VENDOR_ID_VISIONEER,
	VENDOR_ID_CANON,
	VENDOR_ID_SKYWORTH,
	VENDOR_ID_NIKON,
	VENDOR_ID_CYPRESS,
	VENDOR_ID_ROHM,
	VENDOR_ID_COMPAL_ELEC,
	VENDOR_ID_SEIKO_EPSON,
	VENDOR_ID_IODATA_DEVICE,
	VENDOR_ID_FUJIFILM,
	VENDOR_ID_ST_ERICSSON,
	VENDOR_ID_MENTOR,
	VENDOR_ID_MICROCHIP,
	VENDOR_ID_HOLTEK,
	VENDOR_ID_PANASONIC,
	VENDOR_ID_HUANHSIN, 
	VENDOR_ID_EXAR,
	VENDOR_ID_SCM_MICRO,
	VENDOR_ID_NORTHSTAR,
	VENDOR_ID_TOKYO,
	VENDOR_ID_VICTOR,
	VENDOR_ID_CHICONY,
	VENDOR_ID_NEWNEX,
	VENDOR_ID_AIKESHI,
	VENDOR_ID_JASON,
	VENDOR_ID_TECONN,
	VENDOR_ID_HUANGJI,
	VENDOR_ID_FOUSINE,
	VENDOR_ID_NINGBO_BROAD,
	VENDOR_ID_XIANHE,
};

#define BUILT_IN_VENDOR_COUNT    (sizeof(builtInVendorIds)/sizeof(builtInVendorIds[0]))

/* max number of supported vendor ids (built-in + 3rd party). increase as needed */
#define VENDOR_COUNT_MAX         1025

int vendorIds[VENDOR_COUNT_MAX];
unsigned vendorIdCount = 0;

int get_adb_usb_ini(char* buff, size_t len);

void usb_vendors_init(const char *fileName)
{
    if (VENDOR_COUNT_MAX < BUILT_IN_VENDOR_COUNT) {
        fprintf(stderr, "VENDOR_COUNT_MAX not big enough for built-in vendor list.\n");
        exit(2);
    }

    /* add the built-in vendors at the beginning of the array */
    memcpy(vendorIds, builtInVendorIds, sizeof(builtInVendorIds));

    /* default array size is the number of built-in vendors */
    vendorIdCount = BUILT_IN_VENDOR_COUNT;

    if (VENDOR_COUNT_MAX == BUILT_IN_VENDOR_COUNT)
        return;

	//FILE *f = fopen("F:\\temp\\t.txt", "wt");
	//fwrite(fileName, strlen(fileName), sizeof(char), f);
	//char ch = '\n';;
	//fwrite((const void*)&ch, 1, 1, f);

	try 
	{
		FILE *fp = fopen(fileName, "rb");
		if (NULL != fp)
		{
			unsigned int uCount = 0;
			fread((void *)&uCount, 4, 1, fp);
			if (VENDOR_COUNT_MAX > uCount)
			{				
				uCount = vendorIdCount;
				while(!feof(fp))
				{
					int vendorId = 0;
					fread((void *)&vendorId, 1, 4, fp);
					unsigned int i = 0;
					for (i = 0; i < uCount; i++)
					{
						if (vendorIds[i]==vendorId)
						{
							break;
						}
					}

					if (i == uCount)
					{
						vendorIds[i] = vendorId;
						uCount++;
					}

					//fwrite((const void*)&vendorId, 4, 1, f);

					if (VENDOR_COUNT_MAX <= uCount)
					{
						break;
					}
				}

			}
			fclose(fp);
			vendorIdCount = uCount-1;
		}
	}
	catch(char *) 
	{
	}

	//fclose(f);

    char temp[MAX_PATH];
    if (get_adb_usb_ini(temp, sizeof(temp)) == 0) {
        //FILE * f = fopen(temp, "rt");

        FILE * f = fopen(temp, "r+t");
        if (f != NULL) {
            /* The vendor id file is pretty basic. 1 vendor id per line.
               Lines starting with # are comments */
            while (fgets(temp, sizeof(temp), f) != NULL) {
                if (temp[0] == '#')
                    continue;

                long value = strtol(temp, NULL, 0);
                if (errno == EINVAL || errno == ERANGE || value > INT_MAX || value < 0) {
                    fprintf(stderr, "Invalid content in %s. Quitting.\n", ANDROID_ADB_INI);
                    exit(2);
                }

                vendorIds[vendorIdCount++] = (int)value;

                /* make sure we don't go beyond the array */
                if (vendorIdCount == VENDOR_COUNT_MAX) {
                    break;
                }
            }
        }
    }


}

/* Utils methods */

/* builds the path to the adb vendor id file. returns 0 if success */
int build_path(char* buff, size_t len, const char* format, const char* home)
{
	if (sprintf(buff, format, home, ANDROID_PATH, ANDROID_ADB_INI) >= (signed)len) {
		return 1;
	}

	return 0;
}

/* fills buff with the path to the adb vendor id file. returns 0 if success */
int get_adb_usb_ini(char* buff, size_t len)
{
#ifdef _WIN32
    const char* home = getenv("ANDROID_SDK_HOME");
    if (home != NULL) {
        return build_path(buff, len, "%s\\%s\\%s", home);
    } else {
        char path[MAX_PATH];
        SHGetFolderPathA( NULL, CSIDL_PROFILE, NULL, 0, path);
        return build_path(buff, len, "%s\\%s\\%s", path);
    }
#else
    const char* home = getenv("HOME");
    if (home == NULL)
        home = "/tmp";

    return build_path(buff, len, "%s/%s/%s", home);
#endif
}
