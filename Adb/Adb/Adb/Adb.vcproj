<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="Adb"
	ProjectGUID="{A105B016-4882-4E92-B1A1-57431930743D}"
	RootNamespace="Adb"
	SccProjectName="SAK"
	SccAuxPath="SAK"
	SccLocalPath="SAK"
	SccProvider="SAK"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="../AdbWinApi;../AdbWinUsbApi"
				PreprocessorDefinitions="__inline__=__inline;WIN32;_DEBUG;_CONSOLE;HAVE_WINSOCK;ADB_HOST;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS;HAVE_WIN32_PROC;HAVE_WIN32_IPC;_XOPEN_SOURCE;_GNU_SOURCE;ADB_TRACE"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="../lib/zlib.lib ../lib/Ws2_32.lib ../lib/ssleay32.lib ../lib/libeay32.lib ../Debug/AdbWinApi.lib ../Debug/AdbWinUsbApi.lib"
				LinkIncremental="2"
				GenerateManifest="false"
				GenerateDebugInformation="true"
				SubSystem="1"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
				AllowIsolation="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
				EmbedManifest="false"
				UseFAT32Workaround="false"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../AdbWinApi;../AdbWinUsbApi"
				PreprocessorDefinitions="WIN32;NDEBUG;_CONSOLE;__inline__=__inline;HAVE_WINSOCK;ADB_HOST;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS;HAVE_WIN32_PROC;HAVE_WIN32_IPC;_XOPEN_SOURCE;_GNU_SOURCE;ADB_TRACE"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="../lib/zlib.lib ../lib/Ws2_32.lib ../lib/ssleay32.lib ../lib/libeay32.lib ../Release/AdbWinApi.lib ../Release/AdbWinUsbApi.lib"
				LinkIncremental="1"
				GenerateDebugInformation="true"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\Adb.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_auth_client.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_auth_host.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_client.cpp"
				>
			</File>
			<File
				RelativePath=".\backup_service.cpp"
				>
			</File>
			<File
				RelativePath=".\base64.cpp"
				>
			</File>
			<File
				RelativePath=".\centraldir.cpp"
				>
			</File>
			<File
				RelativePath=".\commandline.cpp"
				>
			</File>
			<File
				RelativePath=".\console.cpp"
				>
			</File>
			<File
				RelativePath=".\fdevent.cpp"
				>
			</File>
			<File
				RelativePath=".\file_sync_client.cpp"
				>
			</File>
			<File
				RelativePath=".\file_sync_service.cpp"
				>
			</File>
			<File
				RelativePath=".\framebuffer_service.cpp"
				>
			</File>
			<File
				RelativePath=".\get_my_path_darwin.cpp"
				>
			</File>
			<File
				RelativePath=".\get_my_path_freebsd.cpp"
				>
			</File>
			<File
				RelativePath=".\get_my_path_windows.cpp"
				>
			</File>
			<File
				RelativePath=".\jdwp_service.cpp"
				>
			</File>
			<File
				RelativePath=".\list.cpp"
				>
			</File>
			<File
				RelativePath=".\log_service.cpp"
				>
			</File>
			<File
				RelativePath=".\remount_service.cpp"
				>
			</File>
			<File
				RelativePath=".\rsa.cpp"
				>
			</File>
			<File
				RelativePath=".\services.cpp"
				>
			</File>
			<File
				RelativePath=".\sockets.cpp"
				>
			</File>
			<File
				RelativePath=".\stdafx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\sysdeps_win32.cpp"
				>
			</File>
			<File
				RelativePath=".\transport.cpp"
				>
			</File>
			<File
				RelativePath=".\transport_local.cpp"
				>
			</File>
			<File
				RelativePath=".\transport_usb.cpp"
				>
			</File>
			<File
				RelativePath=".\usb_vendors.cpp"
				>
			</File>
			<File
				RelativePath=".\usb_windows.cpp"
				>
			</File>
			<File
				RelativePath=".\utils.c"
				>
			</File>
			<File
				RelativePath=".\zipfile.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\adb.h"
				>
			</File>
			<File
				RelativePath=".\adb_auth.h"
				>
			</File>
			<File
				RelativePath=".\adb_client.h"
				>
			</File>
			<File
				RelativePath=".\base64.h"
				>
			</File>
			<File
				RelativePath=".\dirent.h"
				>
			</File>
			<File
				RelativePath=".\fdevent.h"
				>
			</File>
			<File
				RelativePath=".\file_sync_service.h"
				>
			</File>
			<File
				RelativePath=".\list.h"
				>
			</File>
			<File
				RelativePath=".\mutex_list.h"
				>
			</File>
			<File
				RelativePath=".\private.h"
				>
			</File>
			<File
				RelativePath=".\rsa.h"
				>
			</File>
			<File
				RelativePath=".\sockets.h"
				>
			</File>
			<File
				RelativePath=".\stdafx.h"
				>
			</File>
			<File
				RelativePath=".\sysdeps.h"
				>
			</File>
			<File
				RelativePath=".\transport.h"
				>
			</File>
			<File
				RelativePath=".\unistd.h"
				>
			</File>
			<File
				RelativePath=".\usb100.h"
				>
			</File>
			<File
				RelativePath=".\usb_vendors.h"
				>
			</File>
			<File
				RelativePath=".\utils.h"
				>
			</File>
			<File
				RelativePath=".\zconf.h"
				>
			</File>
			<File
				RelativePath=".\zipfile.h"
				>
			</File>
			<File
				RelativePath=".\zlib.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
		<File
			RelativePath=".\ReadMe.txt"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
