<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="AdbWinApi"
	ProjectGUID="{86C4C8AF-7057-4E8E-B002-8A432F698A0D}"
	RootNamespace="AdbWinApi"
	SccProjectName="SAK"
	SccAuxPath="SAK"
	SccLocalPath="SAK"
	SccProvider="SAK"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="C:\WinDDK\7600\inc\api;C:\WinDDK\7600\inc\ddk"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;ADBWINAPI_EXPORTS;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="setupapi.lib"
				LinkIncremental="2"
				GenerateManifest="false"
				ModuleDefinitionFile="AdbWinApi.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="C:\WinDDK\7600\inc\api;C:\WinDDK\7600\inc\ddk"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;ADBWINAPI_EXPORTS;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS"
				RuntimeLibrary="2"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="setupapi.lib"
				LinkIncremental="1"
				ModuleDefinitionFile="AdbWinApi.def"
				GenerateDebugInformation="true"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\adb_api.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_api_instance.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_endpoint_object.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_helper_routines.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_interface.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_interface_enum.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_io_completion.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_legacy_endpoint_object.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_legacy_interface.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_legacy_io_completion.cpp"
				>
			</File>
			<File
				RelativePath=".\adb_object_handle.cpp"
				>
			</File>
			<File
				RelativePath=".\AdbWinApi.cpp"
				>
			</File>
			<File
				RelativePath=".\AdbWinApi.def"
				>
			</File>
			<File
				RelativePath=".\stdafx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\adb_api.h"
				>
			</File>
			<File
				RelativePath=".\adb_api_instance.h"
				>
			</File>
			<File
				RelativePath=".\adb_api_legacy.h"
				>
			</File>
			<File
				RelativePath=".\adb_api_private_defines.h"
				>
			</File>
			<File
				RelativePath=".\adb_endpoint_object.h"
				>
			</File>
			<File
				RelativePath=".\adb_helper_routines.h"
				>
			</File>
			<File
				RelativePath=".\adb_interface.h"
				>
			</File>
			<File
				RelativePath=".\adb_interface_enum.h"
				>
			</File>
			<File
				RelativePath=".\adb_io_completion.h"
				>
			</File>
			<File
				RelativePath=".\adb_legacy_endpoint_object.h"
				>
			</File>
			<File
				RelativePath=".\adb_legacy_interface.h"
				>
			</File>
			<File
				RelativePath=".\adb_legacy_io_completion.h"
				>
			</File>
			<File
				RelativePath=".\adb_object_handle.h"
				>
			</File>
			<File
				RelativePath=".\adb_winusb_api.h"
				>
			</File>
			<File
				RelativePath=".\Resource.h"
				>
			</File>
			<File
				RelativePath=".\stdafx.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
			<File
				RelativePath=".\AdbWinApi.rc"
				>
			</File>
		</Filter>
		<File
			RelativePath=".\ReadMe.txt"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
