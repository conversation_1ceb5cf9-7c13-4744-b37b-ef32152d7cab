/*
 * Copyright (C) 2006 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

//Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "winres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE 9, 1
#pragma code_page(1252)
#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE  
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE  
BEGIN
    "#include ""winres.h""\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED

#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 2,0,0,0
 PRODUCTVERSION 2,0,0,0
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904e4"
        BEGIN
            VALUE "CompanyName", "Google, inc"
            VALUE "FileDescription", "Android ADB API"
            VALUE "FileVersion", "*******"
            VALUE "LegalCopyright", "Copyright (C) 2006 The Android Open Source Project"
            VALUE "InternalName", "AdbWinApi.dll"
            VALUE "OriginalFilename", "AdbWinApi.dll"
            VALUE "ProductName", "Android SDK"
            VALUE "ProductVersion", "*******"
            VALUE "OLESelfRegister", ""
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
		VALUE "Translation", 0x0409, 1252
    END
END

#endif    // !_MAC

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE  
BEGIN
	IDS_PROJNAME					"AdbWinApi"
END

////////////////////////////////////////////////////////////////////////////


#endif

#ifndef APSTUDIO_INVOKED
#endif    // not APSTUDIO_INVOKED
